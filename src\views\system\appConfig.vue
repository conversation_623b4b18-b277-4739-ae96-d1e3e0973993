<!--
 * @Date: 2025-06-26 09:35:16
 * @LastEditors: linqh21
 * @LastEditTime: 2025-07-30 18:11:29
 * @Description:
 * @FilePath: \src\views\system\appConfig.vue
-->
<template>
  <el-row :gutter="10">
    <el-col :xs="24" :sm="6" :md="4" :lg="4" :xl="5">
      <div class="box">
        <el-scrollbar>
          <basic-container>
            <avue-tree ref="tree" :option="treeOption" :data="treeData" @node-click="nodeClick" node-key="id" :current-node-key="treeGridId" />
          </basic-container>
        </el-scrollbar>
      </div>
    </el-col>
    <el-col :xs="24" :sm="9" :md="8" :lg="8" :xl="7">
      <basic-container>
        <div class="iphone-mockup">
          <div class="iphone-top-bar"></div>
          <div class="iphone-AppList" @click="onAppListClick">
            <div class="app-grid6">
              <div v-for="app in appList.slice(0,5)" :key="app.menuId" class="app-grid6-item">
                <div class="app-grid6-icon" :style="{background: app.bgColor || '#e3f1fd'}">
                  <img :src="app.menuSourceLink" alt="icon" />
                </div>
                <div class="app-grid6-name">{{ app.menuName }}</div>
              </div>
              <div v-if="appList.length >= 5" class="app-grid6-item more-item">
                <div class="app-grid6-icon more-icon">
                  <img src="@/assets/appConfig/menu-more.png" alt="icon" />
                </div>
                <div class="app-grid6-name">更多</div>
              </div>
            </div>
          </div>
          <div class="iphone-content">
            <div class="content-blocks-wrapper">
              <draggable v-model="contentList" :animation="200" class="content-draggable" :ghost-class="'content-dragging'" :chosen-class="'content-chosen'">
                <div class="color-block" v-for="(item, idx) in contentList" :key="item.id" :class="item.id" :style="getBlockStyle(item.id)">
                  <span class="content-remove-btn" @click.stop="removeContentBlock(idx)">×</span>
                  <template v-if="item.id === 'ConvenientService'">
                    <!-- 便民服务特殊布局 -->
                    <div class="convenient-service-container">
                      <div class="convenient-service-title">便民服务</div>
                      <div class="convenient-service-cards">
                        <div class="service-card work-instructions">
                          <div class="service-card-content">
                            <div class="service-card-title">办事指南</div>
                          </div>
                        </div>
                        <div class="service-card discount-policy">
                          <div class="service-card-content">
                            <div class="service-card-title">涉农政策</div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </template>
                  <template v-else-if="item.img">
                    <img :src="item.img" alt="" style="width:100%;height:100%;object-fit:cover;" />
                  </template>
                  <template v-else>
                    {{ idx+1 }}
                  </template>
                </div>
              </draggable>
            </div>
            <div class="empty-content-card" @click="onEmptyCardClick">
              点击添加组件
            </div>
          </div>
          <div class="iphone-bottom-bar">
            <div class="nav-item active">
              <img src="@/assets/appConfig/home_on.png" alt="首页" class="nav-icon">
              <span class="nav-text">首页</span>
            </div>
            <div class="nav-item">
              <img src="@/assets/appConfig/mine.png" alt="我的" class="nav-icon">
              <span class="nav-text">我的</span>
            </div>
          </div>
        </div>
      </basic-container>
    </el-col>
    <el-col :xs="24" :sm="9" :md="12" :lg="12" :xl="12">

      <basic-container>
        <div class="appListBox" v-show="!showContentListBox">
          <div class="app-title-row">
            <div class="app-title">
              <span class="dept-label">{{ currentDeptName || '' }}</span>
              <span>应用配置</span>
            </div>
            <el-button class="app-save-btn" type="primary" size="mini" :disabled="appList.length == 0" @click="setAppConfigList">保存配置</el-button>
          </div>
          <div class="app-title-divider"></div>
          <div class="app-grid">
            <!-- <template v-for="(app) in appList">
              <div v-if="app.disabled" :key="app.id" class="app-item">
                <span class="app-disabled-tag">上级应用</span>
                <img :src="app.menuSourceLink" alt="app icon" class="app-icon" />
                <div class="app-name-tag">{{ app.menuName }}</div>
              </div>
            </template> -->

            <draggable v-model="appList" :animation="200" class="app-draggable" :ghost-class="'app-dragging'" :chosen-class="'app-chosen'">
              <template v-for="(app,idx) in appList">
                <div v-if="!app.disabled" :key="app.menuId" class="app-item">
                  <span class="app-remove" @click.stop="removeApp(idx)">×</span>
                  <img :src="app.menuSourceLink" alt="app icon" class="app-icon" />
                  <div class="app-name-tag">{{ app.menuName }}</div>
                </div>
                <div v-else :key="app.menuId" class="app-item">
                  <span class="app-disabled-tag">镇村联动</span>
                  <img :src="app.menuSourceLink" alt="app icon" class="app-icon" />
                  <div class="app-name-tag">{{ app.menuName }}</div>
                </div>
              </template>
              <!-- <div v-for="(app, idx) in appList" :key="app.id" class="app-item">
                <span v-if="!app.disabled" class="app-remove" @click.stop="removeApp(idx)">×</span>
                <span v-else class="app-disabled-tag">上级应用</span>
                <img :src="app.menuSourceLink" alt="app icon" class="app-icon" />
                <div class="app-name-tag">{{ app.menuName }}</div>
              </div> -->
            </draggable>
            <div class="app-item app-add" @click="openAddDialog">
              <div class="add-icon">+</div>
              <div class="app-name">新增</div>
            </div>
          </div>
        </div>
        <div class="contentListBox" v-show="showContentListBox">
          <div class="content-title-row">
            <div class="content-title">
              <span class="dept-label">{{ currentDeptName || '' }}</span>
              <span>内容配置</span>
            </div>
            <el-button class="content-save-btn" type="primary" size="mini" @click="saveContentConfig">保存配置</el-button>
          </div>
          <div class="content-title-divider"></div>
          <div class="content-blocks-container">
            <div v-for="(block, idx) in contentBlocks" :key="block.id" class="color-block" :class="block.id" @mouseenter="block.hover = true" @mouseleave="block.hover = false" :style="getBlockStyle(block.id)">
              <template v-if="block.id === 'ConvenientService'">
                <!-- 便民服务特殊布局 -->
                <div class="convenient-service-container">
                  <div class="convenient-service-title">便民服务</div>
                  <div class="convenient-service-cards">
                    <div class="service-card work-instructions">
                      <div class="service-card-content">
                        <div class="service-card-title">办事指南</div>
                      </div>
                    </div>
                    <div class="service-card discount-policy">
                      <div class="service-card-content">
                        <div class="service-card-title">涉农政策</div>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
              <template v-else-if="block.img">
                <img :src="block.img" alt="" style="width:100%;height:100%;object-fit:cover;" />
              </template>
              <template v-else>
                {{ idx+1 }}
              </template>
              <el-button v-if="block.hover" class="select-btn" size="mini" type="primary" @click.stop="addBlockToContentList(block)">
                选择
              </el-button>
            </div>
          </div>
        </div>
      </basic-container>
      <!-- 新增弹窗 -->
      <el-dialog title="选择应用" :visible.sync="dialogVisible" width="420px" :close-on-click-modal="false" append-to-body @close="closeAddDialog">
        <div class="tree-dialog-content">
          <el-tree v-if="dialogVisible" ref="addTree" :data="menuMiniIdTree" node-key="id" :props="{ label: 'title', children: 'children' }" show-checkbox highlight-current :default-expand-all="true" :expand-on-click-node="false" :default-checked-keys="defaultCheckedKeys" @check-change="onTreeCheckChange" class="tree-select">
            <span slot-scope="{ node, data }">
              <span>{{ node.label +`${data.isLinkage ? ' （镇村联动）' : ''}`}}</span>
            </span>
          </el-tree>
        </div>
        <div class="tree-dialog-footer">
          <el-button size="mini" @click="closeAddDialog">取消</el-button>
          <el-button size="mini" type="primary" :disabled="!hasCheckedNodes" @click="confirmAddApp">确定</el-button>
        </div>
      </el-dialog>
    </el-col>
  </el-row>
</template>

<script>
import { getDeptTree } from "@/api/system/dept";
import draggable from 'vuedraggable';
import { getAppTree, getAppConfig, setAppConfig,getContentConfig, setContentConfig } from "@/api/system/menu";
import { mapGetters } from "vuex";
// 工具函数：合并并去重应用列表，优先保留disabled=true的项
function mergeAppList(townList, villageList, linkageIds) {
  const villageMap = new Map(villageList.map(item => [item.menuId, item]));

  townList
    .filter(item => linkageIds.includes(item.menuId))
    .forEach(item => {
      villageMap.set(item.menuId, {
        ...(villageMap.get(item.menuId) || item),
        disabled: true
      });
    });
  console.log(villageMap);

  return Array.from(villageMap.values());
}

export default {
  components: { draggable },
  data() {
    return {
      treeGridId: "",
      treeData: [],
      treeOption: {
        nodeKey: "id",
        defaultExpandAll: false,
        addBtn: false,
        menu: false,
        size: "small",
        props: {
          label: "title",
          value: "id",
          children: "children",
        },
      },
      appList: [],
      menuMiniIdTree: [],
      dialogVisible: false,
      checkedCount: 0,
      currentDeptName: '',
      isTown: false,
      defaultCheckedKeys: [],
      disabledKeys: [],
      contentList: [],
      showContentListBox: false,
      contentBlocks: [
        { id: 'ConvenientService', img: '', hover: false },
        { id: 'ThreeAffairsDisclosure', img: '', hover: false },
        { id: 'RuralDynamics', img: '', hover: false },
        { id: 'Monitoring', img: '', hover: false }
      ],
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    hasCheckedNodes() {
      return this.checkedCount > 0;
    }
  },
  mounted() {
    // console.log('userInfo:', this.userInfo.tenant_id);

    this.initData();
  },

  methods: {
    // 初始化部门树和菜单树数据
    initData() {
      Promise.all([getDeptTree(true), getAppTree()]).then(([deptRes, menuRes]) => {
        this.treeData = deptRes.data.data;
        this.menuMiniIdTree = menuRes.data.data;
      });
    },
    /**
     * 获取当前部门的应用配置列表
     * - 镇级只显示 townAppList
     * - 村级合并镇级（disabled）和村级应用，去重
     */
    getAppConfigList() {
      getAppConfig(this.treeGridId).then(res => {
        if (this.isTown) {
          this.appList = res.data.data.townAppList;
        } else {
          this.appList = mergeAppList([], res.data.data.villageAppList, res.data.data.linkageIds ? res.data.data.linkageIds : []);
          // console.log(this.appList)
        }
      });
      getContentConfig(this.treeGridId).then(res => {
        // console.log(res);
        
        // let temp = {},
        let temp = []
        for(let i = 0; i < res.data.data.length; i++){
          temp.push({
            id: res.data.data[i].code,
            img: '', 
            hover: false
          })
        }
        this.contentList = temp;
      });
    },
    // 处理树节点点击，切换部门
    nodeClick(data) {
      console.log(data);
      //切换时候要删除menuMiniIdTree树中节点的disabled属性
      this.menuMiniIdTree.forEach(item => {
        item.disabled = false;
      })
      this.appList = []
      this.treeGridId = data.id;
      this.isTown = data.hasChildren;
      this.currentDeptName = data.title || data.label || '';
      this.$refs.tree.setCurrentKey(data.id);
      this.getAppConfigList();
    },
    // 打开新增应用弹窗，设置已选项和禁用项
    openAddDialog() {
      if (!this.treeGridId) {
        this.$message && this.$message.warning('请先选择左侧村镇');
        return;
      }
      this.checkedCount = 0;
      this.defaultCheckedKeys = this.appList.map(item => item.menuId);
      if (!this.isTown) {
        // 设置树中disabled属性
        this.appList.forEach(app => {
          if (app.disabled) {
            this.menuMiniIdTree.forEach(menu => {
              if (menu.id === app.menuId) menu.disabled = true;
            });
          }
        });
      }
      // console.log(this.appList,"appList",this.defaultCheckedKeys,"defaultCheckedKeys");
      this.dialogVisible = true;
    },
    closeAddDialog() {
      this.dialogVisible = false;
      this.defaultCheckedKeys = []
      this.checkedCount = 0;
    },
    // 确认新增应用，去重添加
    confirmAddApp() {
      // 先保留appList中disabled为true的项，去除其他项目，然后再根据选择结果添加
      this.appList = this.appList.filter(item => item.disabled);
      const checkedNodes = this.$refs.addTree.getCheckedNodes();
      checkedNodes.forEach(node => {
        //先判断node.id是否与this.appList数组中项目的menuId相等,相等直接继续循环
        if (this.appList.some(item => item.menuId === node.id)) {
          return;
        }
        this.appList.push({
          menuName: node.title,
          menuSourceLink: node.sourceLink,
          menuId: node.id
        });
      });
      this.closeAddDialog();
    },
    // 移除应用
    removeApp(idx) {
      this.appList.splice(idx, 1);
    },
    // 监听树选中变化，更新已选数量
    onTreeCheckChange() {
      this.checkedCount = (this.$refs.addTree.getCheckedNodes() || []).length;
    },
    // 保存应用配置，增加异常处理
    setAppConfigList() {
      const data = {
        deptId: this.treeGridId,
        //menuIds为appList先过滤disabled为true的,在map出menuId数组
        menuIds: this.appList.map(item => item.menuId)
      };
      setAppConfig(data).then(res => {
        if (res.data.success) {
          this.$message.success('保存成功');
        } else {
          this.$message.error('保存失败');
        }
      }).catch(() => {
        this.$message.error('保存异常');
      });
    },
    onEmptyCardClick() {
      if (!this.treeGridId) {
        this.$message && this.$message.warning('请先选择左侧村镇');
        return;
      }
      // 这里可以写点击后的逻辑
      this.showContentListBox = true;
      // this.$message && this.$message.info('你点击了空内容卡片');
    },
    onAppListClick() {
      this.showContentListBox = false;
    },
    getBlockStyle(id) {
      if (id === 'ConvenientService') {
        return {
          background: 'transparent',
          width: '350px',
          height: '125px',
          padding: '0',
          display: 'flex',
          gap: '8px',
        };
      } else if (id === 'ThreeAffairsDisclosure') {
        return {
          background: '#b6e6fd',
          width: '350px',
          height: '300px',
        };
      } else if (id === 'RuralDynamics') {
        return {
          background: '#b6f3e2',
          width: '350px',
          height: '300px',
        };
      } else if (id === 'Monitoring') {
        return {
          background: '#ffe6b6',
          width: '350px',
          height: '300px',
        };
      }
      return {};
    },
    saveContentConfig() {
      console.log(this.contentList);
      // return
      // 这里可以写保存内容配置的逻辑
      // this.$message.success('内容配置已保存');
      // const data = [];
      let data = {
        codes: this.contentList.map(item => item.id),
        deptId: this.treeGridId,
      };
      console.log(data)
      // return
      setContentConfig(data).then(res => {
        if (res.data.success) {
          this.$message.success('保存成功');
        } else {
          this.$message.error('保存失败');
        }
      }).catch(() => {
        this.$message.error('保存异常');
      });
    },
    addBlockToContentList(block) {
      // 判断是否已存在，避免重复添加
      if (!this.contentList.some(item => item.id === block.id)) {
        this.contentList.push({ ...block });
      } else {
        this.$message && this.$message.info('该内容已添加');
      }
    },
    removeContentBlock(idx) {
      this.contentList.splice(idx, 1);
    }
  }
}
</script>

<style scoped>
/* ================== 卡片样式 ================== */
.app-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  gap: 16px;
  padding: 20px;
  /* min-height: 400px; */
}

/* 响应式调整应用网格 */
@media (max-width: 1200px) {
  .app-grid {
    grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
    gap: 12px;
    padding: 16px;
  }
}

@media (max-width: 992px) {
  .app-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
    gap: 10px;
    padding: 12px;
  }
}

@media (max-width: 768px) {
  .app-grid {
    grid-template-columns: repeat(auto-fill, minmax(70px, 1fr));
    gap: 8px;
    padding: 10px;
  }
}
.app-item {
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  aspect-ratio: 1 / 1;
  min-width: 0;
  min-height: 0;
  position: relative;
  padding: 0;
  overflow: hidden;
}
.app-item:hover {
  box-shadow: 0 6px 24px rgba(58, 142, 230, 0.1);
  transform: translateY(-2px) scale(1.03);
}
.app-disabled-tag {
  position: absolute;
  top: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.92);
  border: 1px solid #b6e0fe;
  border-radius: 0 8px 0 12px;
  padding: 0 8px;
  font-size: 11px;
  color: #3a8ee6;
  font-weight: 400;
  z-index: 2;
  height: 22px;
  line-height: 22px;
  min-width: 40px;
  text-align: center;
  box-shadow: none;
  letter-spacing: 1px;
  pointer-events: none;
  user-select: none;
}
.app-icon {
  width: 100%;
  aspect-ratio: 1 / 1;
  object-fit: cover;
  border-radius: 10px 10px 0 0;
  margin: 0;
  display: block;
}
.app-name-tag {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.96);
  border-top: 1px solid #e0eafc;
  border-radius: 0 0 10px 10px;
  font-size: 12px;
  color: #000;
  opacity: 0.7;
  font-weight: 500;
  height: 20px;
  line-height: 20px;
  box-shadow: none;
  letter-spacing: 1px;
  pointer-events: none;
  user-select: none;
  z-index: 2;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.app-remove {
  position: absolute;
  top: 0;
  right: 0;
  width: 22px;
  height: 20px;
  background: #fff;
  border-radius: 0 10px 0 12px;
  color: red;
  font-size: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 1px 4px rgba(245, 108, 108, 0.06);
  opacity: 0.7;
  z-index: 4;
  border: 1px solid #ffeaea;
  border-width: 0 1.5px 1.5px 0;
  transition: all 0.15s;
  line-height: 1;
  padding: 0;
}
.app-remove:hover {
  background: #f56c6c;
  color: #fff;
  opacity: 1;
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.18);
}

/* 拖拽相关样式 */
.app-draggable {
  display: contents;
}
.app-dragging {
  opacity: 0.5;
}
.app-chosen {
  box-shadow: 0 0 0 2px #409eff;
}

/* 新增按钮样式 */
.app-item.app-add {
  border: 2px dashed #bbb;
  background: #fafbfc;
  color: #bbb;
  cursor: pointer;
  justify-content: center;
  align-items: center;
  transition: border-color 0.2s, color 0.2s;
}
.app-item.app-add:hover {
  border-color: #409eff;
  color: #409eff;
}
.add-icon {
  font-size: 32px;
  font-weight: bold;
  margin-bottom: 8px;
  line-height: 1;
}

/* ================== 弹窗及树样式 ================== */
.tree-dialog-content {
  /* background: #fcfdff; */
  /* border-radius: 12px; */
  padding: 16px;
  min-height: 120px;
  /* border: 1px solid #e8f4fd; */
}

.tree-select {
  min-height: 240px;
  max-height: 400px;
  overflow-y: auto;
  background: #ffffff;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e8eaec;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.tree-dialog-footer {
  border-top: 1px solid #e8f4fd;
  padding: 16px 0 0 0;
  text-align: right;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* ================== 弹窗整体优化 ================== */
::v-deep .el-dialog {
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-width: 380px;
  max-width: 420px;
}
::v-deep .el-dialog__header {
  font-size: 18px;
  font-weight: bold;
  border-bottom: 1px solid #f0f2f7;
  padding-bottom: 8px;
  margin-bottom: 0;
}

/* ================== 弹窗整体优化 ================== */
::v-deep .el-dialog {
  border-radius: 12px;
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.12);
}

::v-deep .el-dialog__header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f0f2f5;
}

::v-deep .el-dialog__title {
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

::v-deep .el-dialog__body {
  padding: 20px 24px 16px;
}


/* ================== 按钮优化 ================== */
::v-deep .tree-dialog-footer .el-button {
  border-radius: 8px;
  min-width: 80px;
  height: 36px;
  font-weight: 500;
  transition: all 0.2s ease;
}

::v-deep .tree-dialog-footer .el-button--default {
  background: #f8fafc;
  border-color: #e2e8f0;
  color: #64748b;
}

::v-deep .tree-dialog-footer .el-button--default:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #475569;
  transform: translateY(-1px);
}

::v-deep .tree-dialog-footer .el-button--primary {
  background: #3b82f6;
  border-color: #3b82f6;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

::v-deep .tree-dialog-footer .el-button--primary:hover {
  background: #2563eb;
  border-color: #2563eb;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(59, 130, 246, 0.3);
}

::v-deep .tree-dialog-footer .el-button--primary.is-disabled {
  background: #cbd5e1;
  border-color: #cbd5e1;
  transform: none;
  box-shadow: none;
}

/* ================== 滚动条优化 ================== */
.tree-select::-webkit-scrollbar {
  width: 6px;
}

.tree-select::-webkit-scrollbar-track {
  background: #f8fafc;
  border-radius: 3px;
}

.tree-select::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.tree-select::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* ================== 其它 ================== */
.app-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 16px 0 0 16px;
}
.app-title {
  font-size: 22px;
  font-weight: bold;
  color: #222;
  letter-spacing: 2px;
  margin: 0;
}
.app-save-btn {
  margin-left: 24px;
  height: 28px;
  font-size: 14px;
  padding: 0 18px;
}
.app-title-divider {
  height: 1.5px;
  background: #ececec;
  margin: 12px 0 18px 0;
  border-radius: 1px;
  width: 100%;
}
.dept-label {
  color: #409eff;
  font-size: 18px;
  font-weight: 500;
  margin-right: 18px;
}

.miniapp-banner {
  width: 100%;
  height: 140px;
  border-radius: 16px;
  overflow: hidden;
  margin-bottom: 16px;
  position: relative;
  background: linear-gradient(135deg, #a8e063 0%, #56ab2f 100%);
}
.miniapp-banner-title {
  position: absolute;
  left: 24px;
  top: 32px;
  z-index: 2;
}
.miniapp-banner-title-main {
  font-size: 28px;
  font-weight: bold;
  color: #fff;
  letter-spacing: 2px;
}
.miniapp-banner-title-sub {
  font-size: 22px;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.18);
  margin-top: 4px;
}
.miniapp-banner-tools {
  position: absolute;
  right: 16px;
  top: 16px;
  z-index: 3;
  display: flex;
  gap: 8px;
}
.iphone-mockup {
  position: relative;
  width: 100%;
  max-width: 390px;
  height: 844px;
  margin: 0 auto;
  background: #f7f7f7;
  border-radius: 32px;
  box-shadow: 0 4px 32px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  border: 1.5px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .iphone-mockup {
    max-width: 350px;
    height: 760px;
  }
}

@media (max-width: 992px) {
  .iphone-mockup {
    max-width: 320px;
    height: 680px;
  }
}

@media (max-width: 768px) {
  .iphone-mockup {
    max-width: 280px;
    height: 600px;
  }
}
.iphone-top-bar {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 200px;
  background: #6ec1e4;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  font-weight: bold;
  z-index: 10;
  background-image: url("~@/assets/appConfig/swiper-def.png");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
}
.iphone-bottom-bar {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 55px;
  background: #fff;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-around;
  font-size: 16px;
  border-top: 1px solid #e0e0e0;
  /* padding: 0 20px; */
  z-index: 10;
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-icon {
  width: 24px;
  height: 24px;
  margin-bottom: 4px;
}

.nav-text {
  font-size: 12px;
  color: #999;
}

.nav-item.active .nav-text {
  color: #4caf50;
}

.iphone-content {
  position: absolute;
  left: 0;
  right: 0;
  top: 370px; /* AppList top:170px + height:180px + 间距20px */
  bottom: 80px; /* 距离底部栏 */
  overflow-y: auto;
  padding: 0 12px;
  z-index: 5;
  margin-top: 25px;
}

/* 响应式调整内容区域 */
@media (max-width: 1200px) {
  .iphone-content {
    top: 340px;
    bottom: 70px;
  }
}

@media (max-width: 992px) {
  .iphone-content {
    top: 310px;
    bottom: 65px;
  }
}

@media (max-width: 768px) {
  .iphone-content {
    top: 280px;
    bottom: 60px;
  }
}
.iphone-AppList {
  position: absolute;
  left: 50%;
  top: 170px;
  transform: translateX(-50%);
  width: 90%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 10px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  padding: 20px 12px 16px 12px;
  min-height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 20;
  border: 1px solid rgba(255, 255, 255, 0.2);
  cursor: pointer;
}
.app-grid6 {
  width: 100%;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 16px 8px;
  padding: 0 8px;
}
.app-grid6-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  user-select: none;
  transition: transform 0.2s ease;
}
.app-grid6-item:hover {
  transform: translateY(-2px);
}
.app-grid6-icon {
  width: 56px;
  height: 56px;
  border-radius: 14px;
  background: #e3f1fd;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: all 0.2s ease;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}
.app-grid6-icon:hover {
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
  transform: scale(1.05);
}
.app-grid6-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.app-grid6-name {
  font-size: 13px;
  color: #333;
  font-weight: 500;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 70px;
  line-height: 1.2;
}
.empty-content-card {
  width: 95%;
  height: 100px;
  margin: 0 auto 0 auto;
  background: #fff;
  border-radius: 16px;
  border: 2px solid #b6e6fd;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
  font-size: 22px;
  cursor: pointer;
  transition: box-shadow 0.2s, border-color 0.2s;
}
.empty-content-card:hover {
  box-shadow: 0 4px 16px rgba(58, 142, 230, 0.1);
  border-color: #6ec1e4;
  color: #6ec1e4;
}
.contentListBox {
  width: 100%;
  /* margin: 10px auto; */
  padding: 0;
}
.content-blocks-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 12px; /* 统一设置块之间的间隔 */
  padding: 8px;
}
.iphone-content .color-block {
  margin: 0; /* 移除原有的margin，使用gap统一控制 */
}
.color-block {
  position: relative;
  border-radius: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  color: #888;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  margin: 0 8px;
  transition: box-shadow 0.2s;
}
.select-btn {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 2;
  padding: 0 10px;
  height: 24px;
  line-height: 24px;
}
.content-title-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin: 16px 0 0 16px;
}
.content-title {
  font-size: 22px;
  font-weight: bold;
  color: #222;
  letter-spacing: 2px;
  margin: 0;
}
.content-save-btn {
  margin-left: 24px;
  height: 28px;
  font-size: 14px;
  padding: 0 18px;
}
.content-title-divider {
  height: 1.5px;
  background: #ececec;
  margin: 12px 0 18px 0;
  border-radius: 1px;
  width: 100%;
}
.content-blocks-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  justify-content: space-between;
  align-items: center;
  padding: 5px;
  background-color: #f5f5f5;
}
/* contentList 区域 color-block 间距和删除按钮样式 */
.contentListBox .color-block {
  margin-right: 16px;
  margin-bottom: 16px;
}
.content-remove-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 22px;
  height: 22px;
  background: #fff;
  border-radius: 50%;
  color: #f56c6c;
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 1px 4px rgba(245, 108, 108, 0.08);
  border: 1px solid #ffeaea;
  transition: all 0.15s;
  z-index: 10;
}
.content-remove-btn:hover {
  background: #f56c6c;
  color: #fff;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.18);
}
/* 内容块拖拽相关样式 */
.content-draggable {
  display: contents;
}

.content-dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.content-chosen {
  box-shadow: 0 0 0 2px #409eff;
  cursor: grabbing;
}

.color-block {
  cursor: grab;
  transition: all 0.2s ease;
}

.color-block:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
.more-item {
  opacity: 0.8;
}

.more-icon {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.more-icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.more-dots {
  font-size: 24px;
  color: #4a9b8e;
  font-weight: bold;
  letter-spacing: 2px;
}

/* ================== 便民服务样式 ================== */
.convenient-service-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
  border-radius: 8px;
  padding: 12px;
  box-sizing: border-box;
}

.convenient-service-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
  text-align: left;
  padding-left: 2px;
}

.convenient-service-cards {
  display: flex;
  gap: 6px;
  flex: 1;
}

.service-card {
  flex: 1;
  border-radius: 5px;
  position: relative;
  overflow: hidden;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: flex-end;
  min-height: 75px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.service-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.service-card.work-instructions {
  /* background: linear-gradient(135deg, #ff9500 0%, #ffb84d 100%); */
  background-image: url("~@/assets/appConfig/work-instructions-bg.png");
  background-blend-mode: overlay;
}

.service-card.discount-policy {
  /* background: linear-gradient(135deg, #007aff 0%, #4da6ff 100%); */
  background-image: url("~@/assets/appConfig/discount-policy-bg.png");
  background-blend-mode: overlay;
}

.service-card-content {
  width: 100%;
  padding: 8px 12px;
  /* background: rgba(0, 0, 0, 0.2); */
  /* backdrop-filter: blur(5px); */
}

.service-card-title {
  font-size: 18px;
  font-weight: bold;
  color: #fff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  line-height: 3;
}

/* 移动端优化 */
@media (max-width: 768px) {
  .convenient-service-container {
    padding: 8px;
  }

  .convenient-service-title {
    font-size: 14px;
    margin-bottom: 6px;
  }

  .service-card {
    min-height: 60px;
    border-radius: 12px;
  }

  .service-card-content {
    padding: 6px 8px;
  }

  .service-card-title {
    font-size: 12px;
    line-height: 1.2;
  }

  /* 移动端布局调整 */
  .iphone-mockup {
    margin-bottom: 20px;
  }

  .box {
    margin-bottom: 15px;
  }
}
</style>






