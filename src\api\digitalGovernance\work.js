import request from '@/router/axios';


/**
 * @description: 工作管理-列表
 * @param {object} params
 * @author:
 */
export const getList = (params) => {
  return request({
    url: '/api/admin/work/page',
    method: 'get',
    params,
  })
}

/**
 * @description: 我的工作-列表
 * @param {object} params
 * @author:
 */
export const getMyList = (params) => {
  return request({
    url: '/api/admin/work/myPage',
    method: 'get',
    params,
  })
}

/**
 * @description: 工作管理-新增
 * @param {object} data
 * @author:
 */
export const save = (data) => {
  return request({
    url: '/api/admin/work/save',
    method: 'post',
    data,
  })
}

/**
 * @description: 工作管理-查看详情
 * @param {object} params
 * @author:
 */
export const detail = (params) => {
  return request({
    url: '/api/admin/work/detail',
    method: 'get',
    params,
  })
}

/**
 * @description: 工作管理-修改
 * @param {object} data
 * @author:
 */
export const update = (data) => {
  return request({
    url: '/api/admin/work/update',
    method: 'put',
    data,
  })
}

/**
 * @description: 我的工作-修改
 * @param {object} data
 * @author:
 */
export const myUpdate = (data) => {
  return request({
    url: '/api/admin/work/myUpdate',
    method: 'post',
    data,
  })
}
/**
 * @description: 我的工作-详情
 * @param {object} data
 * @author:
 */
export const myDetail = (params) => {
  return request({
    url: '/api/admin/work/myDetail',
    method: 'get',
    params,
  })
}
/**
 * @description: 我的工作-转派
 * @param {object} data
 * @author:
 */
export const myTransfer = (data) => {
  return request({
    url: '/api/admin/work/myTransfer',
    method: 'post',
    data,
  })
}

/**
 * @description: 工作管理-删除
 * @param {object} params
 * @author:
 */
export const remove = (params) => {
  return request({
    url: '/api/admin/work/remove',
    method: 'delete',
    params,
  })
}


/**
 * @description: 成员组织树
 * @param {object} params
 * @author:
 */
export const getDeptUserTree = (params) => {
  return request({
    url: '/api/admin/work/group/deptUserTree',
    method: 'get',
    params,
  })
}
// 分组成员列表
export const getGroupList = (params) => {
  return request({
    url: '/api/admin/work/group/page',
    method: 'get',
    params,
  })
}
// 分组成员详情
export const getGroupDet = (params) => {
  return request({
    url: '/api/admin/work/group/detail',
    method: 'get',
    params,
  })
}
// 分组成员新增
export const getGroupSave = (params) => {
  return request({
    url: '/api/admin/work/group/save',
    method: 'post',
    params,
  })
}
// 分组成员更新
export const getGroupUpdate = (params) => {
  return request({
    url: '/api/admin/work/group/update',
    method: 'put',
    params,
  })
}
// 分组成员删除
export const getGroupDel = (params) => {
  return request({
    url: '/api/admin/work/group/remove',
    method: 'delete',
    params,
  })
}

