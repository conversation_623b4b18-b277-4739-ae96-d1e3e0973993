<!--
 * @Author: zhouwj83
 * @Date: 2021-06-22 10:26:35
 * @LastEditors: chenz76
 * @LastEditTime: 2022-08-01 17:29:33
 * @Description: 户籍管理
-->

<template>
  <el-row ref="test">
    <el-col :span="5" v-loading="treeLoading">
      <div class="box">
        <el-scrollbar>
          <basic-container>
            <avue-tree :option="treeOption" :data="treeData" @node-click="nodeClick" show-checkbox>
            </avue-tree>
          </basic-container>
        </el-scrollbar>
      </div>
    </el-col>
    <el-col :span="19">
      <basic-container>
        <avue-crud ref="crud" v-model="form" :option="option" :table-loading="loading" :data="data" :page.sync="page"
          :permission="permissionList" :before-open="beforeOpen" @row-update="rowUpdate" @row-save="rowSave"
          @row-del="rowDel" @search-change="searchChange" @search-reset="searchReset"
          @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange"
          @refresh-change="refreshChange" @on-load="onLoad">
          <template slot="menuLeft">
            <el-button v-if="permission.census_import" type="primary" size="small" icon="el-icon-upload2"
              @click="handleImport">导 入</el-button>
            <el-button v-if="permission.census_export" type="primary" size="small" :icon="icon"
              :disabled="disableButton" @click="handleExport">导 出 </el-button>
            <el-button v-if="permission.census_delete" type="danger" size="small" icon="el-icon-delete" plain
              @click="handleDelete">删 除 </el-button>
          </template>
          <template slot="menu" slot-scope="scope">
            <el-button type="text" size="mini" icon="el-icon-user"
              @click="$refs.censusMember.init(scope.row)">成员管理</el-button>
            <el-button type="text" size="mini" icon="el-icon-delete" @click="$refs.crud.rowDel(scope.row)">删
              除</el-button>
          </template>
        </avue-crud>

        <censusMember ref="censusMember" @reflashTable="reflashTable" />

        <el-dialog title="户籍数据导入" append-to-body :visible.sync="excelBox" width="555px">
          <avue-form v-if="excelBox" v-model="excelForm" :option="excelOption" :upload-before="uploadBefore"
            :upload-error="uploadError" :upload-after="uploadAfter">
            <template slot="excelTemplate">
              <el-button type="primary" @click="handleTemplate"> 点击下载<i class="el-icon-download el-icon--right" />
              </el-button>
            </template>
          </avue-form>
        </el-dialog>
      </basic-container>
    </el-col>
  </el-row>

</template>

<script>
import { getList, getDetail, add, update, remove } from '@/api/governance/census'
import { mapGetters } from 'vuex'
import { getToken } from '@/util/auth'
import { keyTrim } from '@/util/util'
import censusMember from './components/census-member.vue'
import axios from 'axios'
import website from '@/config/website'
import { handleDownload } from '@/util/download';
import { getDeptTree } from "@/api/infoRelease/partyLead"
import { exportBlob } from "@/api/common";
import { downloadXls } from "@/util/util";


// import { noSpace  } from '@/util/validate'
// 提交前需要处理空格的form字段
const keys = ['address']

export default {
  components: {
    censusMember
  },
  directives: {
    'load-more': {
      bind (el, binding) {
        const SELECTWRAP_DOM = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');
        SELECTWRAP_DOM.addEventListener('scroll', function () {
          const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      }
    }
  },
  data () {
    const checkName = (rule, value, callback) => {
      if (value !== '' && value.length > 1 && value.length < 21) {
        if (!value.trim()) {
          callback(new Error('户主姓名不能为纯空格'))
        }
      } else {
        callback(new Error('户主姓名长度在2到20个字符'))
      }
      callback()
    };
    return {
      icon: "el-icon-download el-icon--right",
      disableButton: false,
      selectOptions: {
        current: 1,
        size: 20
      },
      userOptions: [],

      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        border: true,
        index: true,
        labelPosition: 'right',
        viewBtn: true,
        delBtn: false,
        selection: true,
        menuWidth: 330,
        searchMenuSpan: 4,
        labelWidth: 120,
        dialogCustomClass: 'census-dialog',
        column: [
          {
            label: '户主姓名',
            prop: 'householderName',
            width: 180,
            type: 'input',
            maxlength: 20,
            showWordLimit: true,
            span: 24,
            search: true,
            searchSpan: 6,
            rules: [
              {
                required: true,
                validator: checkName,
                trigger: "blur",
              }
            ],
          },
          {
            label: "户主联系方式",
            prop: "contract",
            // hide: true, //隐藏列
            span: 24,
            maxlength: 20,
            showWordLimit: true,
            // rules: [
            //   {
            //     required: false,
            //     message: "请填写正确的户主联系方式",
            //     trigger: "blur",
            //     validator: validatePhone,
            //   },
            // ],
          },

          {
            label: '地址',
            prop: 'address',
            type: 'textarea',
            minRows: 3,
            maxRows: 4,
            maxlength: 250,
            showWordLimit: true,
            span: 24,
            rules: [],
            overHidden: true
          }
        ]
      },
      data: [],
      excelBox: false,
      excelForm: {},
      excelOption: {
        submitBtn: false,
        emptyBtn: false,
        column: [
          {
            label: '模板上传',
            prop: 'excelFile',
            type: 'upload',
            drag: true,
            loadText: '模板上传中，请稍等',
            span: 24,
            propsHttp: {
              res: 'data'
            },
            tip: '请上传 .xls,.xlsx 标准格式文件',
            action: '/api/census/import'
          },
          {
            label: '模板下载',
            prop: 'excelTemplate',
            formslot: true,
            span: 24
          }
        ]
      },
      treeLoading: false,
      treeData: [],
      treeOption: {
        addBtn: false,
        menu: false,
        size: 'small',
        props: {
          labelText: '标题',
          label: 'title',
          value: 'value',
          children: 'children'
        }
      },
      deptId: ''
    }
  },
  computed: {
    ...mapGetters(['permission', 'userInfo']),
    permissionList () {
      return {
        addBtn: this.vaildData(this.permission.census_add, false),
        viewBtn: this.vaildData(this.permission.census_view, false),
        delBtn: this.vaildData(this.permission.census_delete, false),
        editBtn: this.vaildData(this.permission.census_edit, false)
      }
    },
    ids () {
      let ids = []
      this.selectionList.forEach((ele) => {
        ids.push(ele.id)
      })
      return ids.join(',')
    },
  },
  created () {
    this.treeLoading = true
    this.initTreeData(this.userInfo.tenant_id)
  },
  methods: {
    rowSave (row, done, loading) {
      row = keyTrim(row, keys)

      add(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          done()
        },
        (error) => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowUpdate (row, index, done, loading) {
      row = keyTrim(row, keys)

      update(row).then(
        () => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          done()
        },
        (error) => {
          loading()
          window.console.log(error)
        }
      )
    },
    rowDel (row) {
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return remove(row.id)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
        })
    },
    handleDelete () {
      if (this.selectionList.length === 0) {
        this.$message.warning('请选择至少一条数据')
        return
      }
      this.$confirm('确定将选择数据删除?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          return remove(this.ids)
        })
        .then(() => {
          this.onLoad(this.page)
          this.$message({
            type: 'success',
            message: '操作成功!'
          })
          this.$refs.crud.toggleSelection()
        })
    },
    handleExport () {
      this.$confirm('是否导出户籍数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        let downloadUrl = `/api/census/export?${this.website.tokenHeader}=${getToken()}`
        if (this.selectionList.length > 0) {
          downloadUrl += `&ids=${this.ids}`
        }
        const householderName = this.query.householderName || ''
        if (householderName.length > 0) {
          downloadUrl += `&householderName=${householderName}`
        }
        this.icon = "el-icon-loading el-icon--right";
        this.disableButton = true;
        const result = await handleDownload(downloadUrl);
        if (result != null) {
          this.icon = "el-icon-download el-icon--right";
          this.disableButton = false;
        }
      })
    },
    async beforeOpen (done, type) {
      if (['edit', 'view'].includes(type)) {
        await new Promise((resolve) => {
          getDetail(this.form.id).then((res) => {
            const data = res.data.data || {}
            this.form = data
            resolve()
          })
        })
      }
      done()
    },
    searchReset () {
      this.query = {}
      this.onLoad(this.page)
    },
    searchChange (params, done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params)
      done()
    },
    selectionChange (list) {
      this.selectionList = list
    },
    selectionClear () {
      this.selectionList = []
      this.$refs.crud.toggleSelection()
    },
    currentChange (currentPage) {
      this.page.currentPage = currentPage
    },
    sizeChange (pageSize) {
      this.page.pageSize = pageSize
    },
    refreshChange () {
      this.onLoad(this.page, this.query)
    },
    onLoad (page, params = {}) {
      this.loading = true
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then((res) => {
        const data = res.data.data
        this.page.total = data.total
        this.data = data.records
        this.loading = false
        this.selectionClear()
      })
    },
    handleImport () {
      this.excelBox = true
    },
    uploadAfter (res, done) {
      if (!res) {
        this.$message.success('导入成功')
      }
      axios.defaults.timeout = website.timeout
      this.excelForm = {}
      this.excelBox = false
      this.refreshChange()
      done()
    },
    uploadBefore (file, done) {
      axios.defaults.timeout = 600000 //上传前设置超时时间为10分钟
      done()
      return
    },
    uploadError () {
      axios.defaults.timeout = website.timeout
    },
    handleTemplate () {
      exportBlob(`/api/census/module`).then(res => {
        downloadXls(res.data, "户籍导入数据模板.xlsx");
      })
    },
    reflashTable () {
      this.$refs.crud.refreshTable();
    },

    filterNodeMethod (value, data) {
      if (!value) return true
      return data.deptName.indexOf(value.trim()) !== -1
    },
    nodeClick (data) {
      this.query.deptId = data.id
      this.deptId = data.id
      this.page.currentPage = 1
      this.onLoad(this.page, this.query)
    },
    async initTreeData (tenantId) {
      this.treeData = (await getDeptTree(tenantId)).data.data
      this.treeLoading = false
    },
  }
}
</script>

<style lang="scss" scoped>
::v-deep .census-user-option {
  display: flex;
  align-items: center;
  padding: 5px 10px;

  .content {
    margin-left: 10px;
    width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>

<style lang="scss">
.census-dialog .el-textarea .el-input__count {
  height: 25px;
  line-height: 25px;
  bottom: -25px !important;
}

.census-dialog .el-input .el-input__count {
  margin-top: 25px;
}
</style>
