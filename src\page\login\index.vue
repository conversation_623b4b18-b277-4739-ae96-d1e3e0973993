<template>
  <div class="login-container">
    <!-- 背景图片 -->
    <div class="login-background"></div>

    <!-- Logo和标题 - 左上角 -->
    <div class="login-header">
      <div class="login-logo">
        <img src="/img/login/logo.png" alt="logo" />
      </div>
      <div class="login-title-img">
        <img src="/img/login/title.png" alt="八闽数村平台" />
      </div>
    </div>

    <!-- 浮动的登录表单卡片 - 中间靠右 -->
    <div class="login-form-card">
      <div class="login-form-container">
        <!-- 登录表单 -->
        <div v-if="!showForgotPassword && !showLoginSmsVerify">
          <h2 class="form-title">账号密码登录</h2>

          <el-form :model="loginForm" :rules="dynamicLoginRules" ref="loginForm" class="login-form">
            <!-- 账户名输入框 -->
            <el-form-item prop="username" label="账户名">
              <el-input
                v-model="loginForm.username"
                placeholder="请输入账号"
                size="large"
                class="login-input"
                @keyup.enter.native="handleLogin"
              >
              </el-input>
            </el-form-item>

            <!-- 密码输入框 -->
            <el-form-item prop="password" label="密码">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                size="large"
                class="login-input"
                show-password
                @keyup.enter.native="handleLogin"
              >
              </el-input>
            </el-form-item>

            <!-- 图形验证码 -->
            <el-form-item v-if="website.captchaMode" prop="code" class="code-form-item" label="图形验证码">
              <div class="code-input-container">
                <el-input
                  v-model="loginForm.code"
                  placeholder="请输入验证码"
                  size="large"
                  class="code-input"
                  @keyup.enter.native="handleLogin"
                >
                </el-input>
                <div class="captcha-container">
                  <div
                    @click="refreshCode"
                    class="captcha-image"
                    v-if="!loginForm.image"
                  >
                    图形验证码
                  </div>
                  <img
                    v-else
                    :src="loginForm.image"
                    @click="refreshCode"
                    class="captcha-image"
                    alt="验证码"
                  />
                </div>
              </div>
            </el-form-item>

            <!-- 登录按钮 -->
            <el-form-item>
              <el-button
                type="primary"
                size="large"
                class="login-btn"
                @click="handleLogin"
                :loading="loading"
              >
                登录
              </el-button>
            </el-form-item>
          </el-form>

          <!-- 忘记密码链接 -->
          <div class="forgot-password">
            <a href="#" @click="handleForgotPassword">忘记密码?</a>
          </div>
        </div>

        <!-- 登录短信验证码界面 -->
        <div v-else-if="showLoginSmsVerify">
          <h2 class="form-title">双重认证</h2>

          <div class="sms-tip">
            <p>验证码已发送至您绑定的手机号</p>
          </div>

          <el-form :model="loginSmsForm" :rules="loginSmsRules" ref="loginSmsForm" class="login-form">
            <!-- 短信验证码 -->
            <el-form-item prop="smsCode" class="code-form-item" label="短信验证码">
              <div class="code-input-container">
                <el-input
                  v-model="loginSmsForm.smsCode"
                  placeholder="请输入短信验证码"
                  size="large"
                  class="code-input"
                  maxlength="6"
                  @keyup.enter.native="handleLoginWithSms"
                >
                </el-input>
                <el-button
                  type="primary"
                  class="send-code-btn"
                  :disabled="loginSmsCountdown > 0"
                  @click="resendLoginSmsCode"
                  :loading="sendingSms"
                  plain
                >
                  {{ loginSmsCountdown > 0 ? `${loginSmsCountdown}s后重发` : '重新发送' }}
                </el-button>
              </div>
            </el-form-item>

            <!-- 登录按钮 -->
            <el-form-item>
              <el-button
                type="primary"
                size="large"
                class="login-btn"
                @click="handleLoginWithSms"
                :loading="loading"
              >
                确认登录
              </el-button>
            </el-form-item>
          </el-form>

          <!-- 返回账号密码登录链接 -->
          <div class="forgot-password">
            <a href="#" @click="backToPasswordLogin">修改账号密码</a>
          </div>
        </div>

        <!-- 忘记密码表单 -->
        <div v-else>
          <h2 class="form-title">重置密码</h2>

          <el-form :model="forgotForm" :rules="forgotRules" ref="forgotForm" class="login-form">
            <!-- 手机号输入框 -->
            <el-form-item prop="phone" label="手机号">
              <el-input
                v-model="forgotForm.phone"
                placeholder="请输入手机号"
                size="large"
                class="login-input"
                maxlength="11"
              >
              </el-input>
            </el-form-item>

            <!-- 新密码输入框 -->
            <el-form-item prop="newPassword" label="新密码">
              <el-input
                v-model="forgotForm.newPassword"
                type="password"
                placeholder="请输入新密码"
                size="large"
                class="login-input"
                show-password
              >
              </el-input>
            </el-form-item>

            <!-- 确认密码输入框 -->
            <el-form-item prop="confirmPassword" label="确认密码">
              <el-input
                v-model="forgotForm.confirmPassword"
                type="password"
                placeholder="请再次输入新密码"
                size="large"
                class="login-input"
                show-password
              >
              </el-input>
            </el-form-item>

            <!-- 短信验证码 -->
            <el-form-item prop="smsCode" class="code-form-item" label="短信验证码">
              <div class="code-input-container">
                <el-input
                  v-model="forgotForm.smsCode"
                  placeholder="请输入短信验证码"
                  size="large"
                  class="code-input"
                  maxlength="6"
                >
                </el-input>
                <el-button
                  type="primary"
                  class="send-code-btn"
                  :disabled="smsCountdown > 0"
                  @click="sendSmsCode"
                  :loading="sendingSms"
                >
                  {{ smsCountdown > 0 ? `${smsCountdown}s后重发` : '发送验证码' }}
                </el-button>
              </div>
            </el-form-item>

            <!-- 重置密码按钮 -->
            <el-form-item>
              <el-button
                type="primary"
                size="large"
                class="login-btn"
                @click="handleResetPassword"
                :loading="resetting"
              >
                重置密码
              </el-button>
            </el-form-item>
          </el-form>

          <!-- 返回登录链接 -->
          <div class="forgot-password">
            <a href="#" @click="backToLogin">返回登录</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getCaptcha, validCode, forgetPwd, sendLoginSms } from "@/api/user";
import { isvalidatemobile } from "@/util/validate";
import { encrypt } from "@/util/sm2";
import { noSpace, checkChinese, isKeyBoardContinuousChar, passWordLimit } from '@/util/validate';

export default {
  name: "login",
  data() {
    // 手机号验证器
    const validatePhone = (rule, value, callback) => {
      if (isvalidatemobile(value)[0]) {
        callback(new Error(isvalidatemobile(value)[1]));
      } else {
        callback();
      }
    };

    // 密码验证器
    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入密码'))
      } else {
        const keyBoard = isKeyBoardContinuousChar(value)
        const passWord = passWordLimit(value)
        const chinese = checkChinese(value)
        const isNoSpace = noSpace(value)
        if (keyBoard === true) {
          callback(new Error('密码不能含有键盘排序'))
        } else if (passWord === false) {
          callback(new Error('至少包含大写字母、小写字母、数字、特殊字符中的三类字符'))
        } else if (isNoSpace === false) {
          callback(new Error('密码不能含有空格'))
        } else if (chinese === true) {
          callback(new Error('密码不能含有中文'))
        } else {
          callback()
        }
      }
    };

    // 确认密码验证器
    const validateConfirmPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'));
      } else if (value !== this.forgotForm.newPassword) {
        callback(new Error('两次输入密码不一致'));
      } else {
        callback();
      }
    };

    return {
      loading: false,
      showForgotPassword: false, // 是否显示忘记密码表单
      showLoginSmsVerify: false, // 是否显示登录短信验证码界面
      requireLoginSms: true, // 控制是否需要登录短信验证码，true时需要验证码，false时直接登录
      sendingSms: false, // 是否正在发送短信
      resetting: false, // 是否正在重置密码
      smsCountdown: 0, // 短信倒计时
      smsTimer: null, // 短信倒计时定时器
      loginSmsCountdown: 0, // 登录短信倒计时
      loginSmsTimer: null, // 登录短信倒计时定时器
      loginForm: {
        tenantId: "000000", // 租户ID
        deptId: "", // 部门ID
        roleId: "", // 角色ID
        username: "",
        password: "",
        type: "account", // 账号类型
        code: "",
        key: "",
        image: "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",
      },
      // 登录短信验证码表单
      loginSmsForm: {
        smsCode: "", // 短信验证码
        smsKey: "", // 短信验证码的key
      },
      forgotForm: {
        phone: "",
        smsCode: "",
        smsKey: "", // 短信验证码的key
        newPassword: "",
        confirmPassword: ""
      },
      // 登录表单验证规则
      loginRules: {
        username: [
          { required: true, message: "请输入账户名", trigger: "blur" }
        ],
        password: [
          { required: true, message: "请输入密码", trigger: "blur" },
          { min: 1, message: "密码长度最少为1位", trigger: "blur" }
        ]
      },
      // 登录短信验证码表单验证规则
      loginSmsRules: {
        smsCode: [
          { required: true, trigger: ['blur','change'], message: "验证码不能为空" }
        ]
      },
      // 忘记密码表单验证规则
      forgotRules: {
        phone: [
          { required: true, trigger: ['blur','change'], validator: validatePhone }
        ],
        smsCode: [
          { required: true, trigger: ['blur','change'], message: "验证码不能为空" }
        ],
        newPassword: [
          { required: true, trigger: ['blur','change'], validator: validatePassword }
        ],
        confirmPassword: [
          { required: true, trigger: ['blur','change'], validator: validateConfirmPassword }
        ]
      }
    };
  },
  computed: {
    ...mapGetters(["website", "tagWel"]),
    // 动态登录验证规则
    dynamicLoginRules() {
      const rules = { ...this.loginRules };
      // 如果开启验证码模式，添加验证码验证规则
      if (this.website.captchaMode) {
        rules.code = [
          { required: true, message: "请输入验证码", trigger: "blur" }
        ];
      }
      return rules;
    }
  },
  created() {
    this.refreshCode();
  },
  beforeDestroy() {
    // 清除定时器
    if (this.smsTimer) {
      clearInterval(this.smsTimer);
    }
    if (this.loginSmsTimer) {
      clearInterval(this.loginSmsTimer);
    }
  },
  methods: {
    // 刷新验证码
    refreshCode() {
      if (this.website.captchaMode) {
        getCaptcha().then(res => {
          const data = res.data;
          this.loginForm.image = data.image;
          this.loginForm.key = data.key;
        }).catch(() => {
          // 如果获取验证码失败，使用默认图片
          this.loginForm.image = "data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7";
        });
      }
    },

    // 处理登录
    handleLogin() {
      this.$refs.loginForm.validate(valid => {
        if (valid) {
          // 如果需要短信验证码，切换到短信验证码界面并自动发送验证码
          if (this.requireLoginSms) {
            this.showLoginSmsVerify = true;
            // 自动发送登录短信验证码
            this.sendLoginSmsCodeAuto();
            return;
          }

          this.loading = true;

          // 调用登录API
          this.$store.dispatch("LoginByUsername", this.loginForm).then(() => {
            this.$router.push({ path: this.tagWel.value });
            this.loading = false;
          }).catch(() => {
            this.loading = false;
            this.refreshCode();
          });
        }
      });
    },

    // 切换到忘记密码页面
    handleForgotPassword() {
      this.showForgotPassword = true;
    },

    // 返回账号密码登录
    backToPasswordLogin() {
      this.showLoginSmsVerify = false;
      // 清空短信验证码表单
      this.loginSmsForm = {
        smsCode: "",
        smsKey: ""
      };
      // 清除登录短信倒计时
      if (this.loginSmsTimer) {
        clearInterval(this.loginSmsTimer);
        this.loginSmsTimer = null;
      }
      this.loginSmsCountdown = 0;
      // 清空表单验证
      this.$nextTick(() => {
        if (this.$refs.loginSmsForm) {
          this.$refs.loginSmsForm.clearValidate();
        }
      });
    },

    // 自动发送登录短信验证码（切换到短信验证码界面时调用）
    sendLoginSmsCodeAuto() {
      // TODO: 等待后端提供登录短信验证码接口
      console.log('自动发送登录短信验证码 - 待实现');

      // 暂时模拟发送成功，显示界面
      this.$message.success("验证码已发送至您绑定的手机号");

      // 模拟设置smsKey和倒计时
      this.loginSmsForm.smsKey = "temp_key_" + Date.now();
      this.loginSmsCountdown = 60;
      this.loginSmsTimer = setInterval(() => {
        this.loginSmsCountdown--;
        if (this.loginSmsCountdown <= 0) {
          clearInterval(this.loginSmsTimer);
          this.loginSmsTimer = null;
        }
      }, 1000);

      /*
      // 真实接口调用代码（待后端接口完成后启用）
      this.sendingSms = true;

      sendLoginSms(this.loginForm.tenantId, this.loginForm.username).then(res => {
        this.sendingSms = false;
        const data = res.data;
        console.log(data);

        if (data.success) {
          // 保存短信验证码的key
          this.loginSmsForm.smsKey = data.data;
          this.$message.success("验证码已发送至您绑定的手机号");

          // 开始倒计时
          this.loginSmsCountdown = 60;
          this.loginSmsTimer = setInterval(() => {
            this.loginSmsCountdown--;
            if (this.loginSmsCountdown <= 0) {
              clearInterval(this.loginSmsTimer);
              this.loginSmsTimer = null;
            }
          }, 1000);
        } else {
          this.$message.error(data.msg || "发送验证码失败");
        }
      }).catch(error => {
        this.sendingSms = false;
        this.$message.error(error.msg || "发送验证码失败，请稍后重试");
      });
      */
    },

    // 重新发送登录短信验证码
    resendLoginSmsCode() {
      this.sendLoginSmsCodeAuto();
    },

    // 使用短信验证码登录
    handleLoginWithSms() {
      // 检查是否已发送验证码
      if (this.loginSmsForm.smsKey === "") {
        this.$message.error("请先发送验证码");
        return;
      }

      this.$refs.loginSmsForm.validate(valid => {
        if (valid) {
          this.loading = true;

          // 构建登录数据，使用短信验证码登录
          // const loginData = {
          //   tenantId: this.loginForm.tenantId,
          //   phone: this.loginForm.username, // 使用登录表单中的用户名作为手机号
          //   smsKey: this.loginSmsForm.smsKey,
          //   smsCode: this.loginSmsForm.smsCode
          // };

          // 调用短信登录API
          this.$store.dispatch("LoginByUsername", this.loginForm).then(() => {
            this.$router.push({ path: this.tagWel.value });
            this.loading = false;
          }).catch(() => {
            this.loading = false;
            this.refreshCode();
          });
        }
      });
    },

    // 发送短信验证码
    sendSmsCode() {
      // 先验证手机号字段
      this.$refs.forgotForm.validateField('phone', valid => {
        if (!valid) {
          this.sendingSms = true;

          // 调用发送短信验证码的API，手机号需要加密
          validCode(encrypt(this.forgotForm.phone)).then(res => {
            this.sendingSms = false;
            const data = res.data;
            console.log(data);

            if (data.success) {
              // 保存短信验证码的key
              this.forgotForm.smsKey = data.data;
              this.$message.success("验证码发送成功");

              // 开始倒计时
              this.smsCountdown = 60;
              this.smsTimer = setInterval(() => {
                this.smsCountdown--;
                if (this.smsCountdown <= 0) {
                  clearInterval(this.smsTimer);
                  this.smsTimer = null;
                }
              }, 1000);
            } else {
              this.$message.error(data.msg || "发送验证码失败");
            }
          }).catch(error => {
            this.sendingSms = false;
            this.$message.error(error.msg || "发送验证码失败，请稍后重试");
          });
        }
      });
    },

    // 处理重置密码
    handleResetPassword() {
      // 检查是否已发送验证码
      if (this.forgotForm.smsKey === "") {
        this.$message.error("请先发送验证码");
        return;
      }

      this.$refs.forgotForm.validate(valid => {
        if (valid) {
          this.resetting = true;

          // 调用重置密码的API
          const resetData = {
            phone: this.forgotForm.phone,
            smsKey: this.forgotForm.smsKey,
            smsCode: this.forgotForm.smsCode,
            newPassword: this.forgotForm.newPassword
          };

          forgetPwd(resetData).then(res => {
            this.resetting = false;
            const data = res.data;
            console.log(data);

            if (data.success) {
              this.$message.success("修改成功");
              this.backToLogin();
            } else {
              this.$message.error(data.msg || "密码重置失败");
            }
          }).catch(error => {
            this.resetting = false;
            this.$message.error(error.msg || "密码重置失败，请检查验证码是否正确");
          });
        }
      });
    },

    // 返回登录页面
    backToLogin() {
      this.showForgotPassword = false;
      // 清空忘记密码表单
      this.forgotForm = {
        phone: "",
        smsCode: "",
        smsKey: "",
        newPassword: "",
        confirmPassword: ""
      };
      // 清除短信倒计时
      if (this.smsTimer) {
        clearInterval(this.smsTimer);
        this.smsTimer = null;
      }
      this.smsCountdown = 0;
      // 清空表单验证
      this.$nextTick(() => {
        if (this.$refs.forgotForm) {
          this.$refs.forgotForm.clearValidate();
        }
      });
    }
  }
};
</script>

<style lang="scss">
  @import "@/styles/login.scss";
</style>
