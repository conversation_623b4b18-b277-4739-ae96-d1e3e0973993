<!-- 
/**
  * @Author: 林中奇
  * @Date: 2025/07/28
  * @lastAuthor:
  * @lastChangeDate:
  * @Explain: 工作管理-人员分组
  */
 
-->
<template>
  <el-row>
    <el-col :span="24">
      <basic-container>
        <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :search.sync="query"
          v-model="form" ref="crud" :before-open="beforeOpen" :upload-before="uploadBefore" :upload-after="uploadAfter"
          :upload-delete="uploadDelete" :upload-exceed="uploadExceed" :upload-preview="uploadPreview"
          @row-update="rowUpdate" @row-save="rowSave" @row-del="rowDel" @search-change="searchChange"
          @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange"
          @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad" :permission="permissionList">
          <!-- 左侧菜单按钮 -->
          <template slot="menuLeft">
            <el-button size="small" icon="el-icon-plus" plain @click="handleAdd(row, 'add')">
              新增
            </el-button>
            <el-button type="danger" size="small" icon="el-icon-delete" plain @click="handleDelete">
              删除
            </el-button>
          </template>
          <!-- 行操作按钮 -->
          <template slot-scope="{ type, size, row, index }" slot="menu">
            <el-button :type="type" :size="size" @click.stop="$refs.crud.rowView(row, index)"><i class="el-icon-view"></i> 查看</el-button>
            <el-button :type="type" :size="size" @click="handleAdd(row, 'edit')"><i class="el-icon-edit"></i> 编辑</el-button>
            <el-button :type="type" :size="size" @click="rowDel(row)"><i class="el-icon-delete"></i> 删除</el-button>
          </template>
        </avue-crud>
        <el-dialog title="选择人员" :visible.sync="groupVisible" width="40%" :before-close="handleClose"
          :append-to-body="true" :close-on-click-modal="false">
          <person-select :type="true" @confirm="e => groupConfirm(e)" />
        </el-dialog>
        <el-dialog title="人员分组新增" :visible.sync="groupSaveVisible" width="50%" :before-close="handleClose"
          :append-to-body="true" :close-on-click-modal="false">
          <el-form ref="ruleForm" :model="ruleForm" :rules="rules" label-width="100px">
            <el-form-item label="分组名称" prop="name" required>
              <el-input size="mini" v-model.trim="ruleForm.name" placeholder="请输入分组名称" maxlength="30" />
            </el-form-item>
            <el-form-item label="负责人" prop="director" required>
              <el-button size="mini" @click="openGroup()">选择人员</el-button>
            </el-form-item>
            <el-form-item label="负责人姓名" prop="directorName" required>
              <el-input size="mini" v-model.trim="ruleForm.directorName" disabled />
            </el-form-item>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button size="mini" @click="close(false)">取 消</el-button>
            <el-button size="mini" type="primary" @click="groupSave('ruleForm')">确 定</el-button>
          </span>
        </el-dialog>
      </basic-container>
    </el-col>
  </el-row>
</template>

<script>
import { mapGetters } from "vuex";
import { validateFile } from "@/views/components/util";
// 审核状态
import * as funList from "@/api/digitalGovernance/work.js";
import { downloadFileBlob } from '@/util/util';
import PersonSelect from "./personSelect.vue";
export default {
  components: { PersonSelect },
  data () {
    const validateUserId = (rule, value, callback) => {
      if (!value) {
        callback(new Error("请选择负责人"));
      } else {
        callback();
      }
    };
    const generateData = _ => {
      const data = [];
      for (let i = 1; i <= 15; i++) {
        data.push({
          key: i,
          label: `备选项 ${i}`,
          disabled: i % 4 === 0
        });
      }
    }
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      selectionList: [],
      option: {
        tip: false,
        searchShow: true,
        searchMenuSpan: 8,
        border: true,
        index: true,
        selection: true,
        // selectable: (row) => {
        //   return row.status == 0;
        // },
        dialogWidth: 1200,
        dialogClickModal: false,
        viewBtn: false,
        delBtn: false, // 默认是有删除、编辑按钮
        editBtn: false,
        addtBtn: false,
        column: [
          {
            label: "分组名称",
            prop: "name",
            searchSpan: 10,
            span: 12,
            search: true,
          },
          { label: "分组人数", prop: "num", addDisabled: false, editDisabled: false, filterable: false },
          { label: "创建人", prop: "createUserName", addDisabled: false, editDisabled: false, filterable: false },
          { label: "负责人姓名", prop: "createName", hide: true },

        ],
      },
      data: [],
      groupSaveVisible: false,
      groupVisible: false,
      ruleForm: {},
      rules: {
        name: [{ required: true, message: "请输入分组名称", trigger: "blur" }],
        director: [{ required: true, message: "请选择负责人" }],
        directorName: [{ required: true, message: "请选择负责人", trigger: "change" }]
      },
      director: '', // 负责人数据
      transferData: generateData()
    };
  },
  watch: {
    // 监听form.userId变化
    "form.userId" (newValue) {
      // 当newValue发生变化时，执行相应的操作
      console.log("form.userId发生变化：", newValue);
      if (newValue) {
        // console.log(this.$refs.crud.$refs)
        this.$nextTick(() => {
          console.log(this.$refs.crud.clearValidate)
          if (this.$refs.crud.clearValidate) {
            this.$refs.crud.clearValidate(["userId"]);
          }
        });
        // 重新校验表单userId
        // this.$refs.crud.$refs.cellForm.clearValidate(["userId"]);
      }
      // 在这里可以执行你想要的操作，例如更新其他属性的值
      // this.form.userName = newValue;
    },
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    // 优化后的权限计算属性
    permissionList () {
      return {
        addBtn: false,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
      };
    },
    ids () {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created () {
    console.log(this.permission);
  },
  mounted () { },
  methods: {
    handleAdd (row, type) {
      this.groupSaveVisible = true
      if (type == 'add') {

      } else {

      }
    },
    openGroup () {
      this.groupVisible = true

    },
    groupSave (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          infoCheck(this.ruleForm).then(res => {
            this.$message({
              type: 'success',
              message: '操作成功!'
            })
            this.$refs[formName].resetFields()
            this.onLoad(this.page);
            this.handleClose()
          })
        } else {
          return false
        }
      })

    },
    groupConfirm (data) {
      console.log('data', data);

      this.director = data.selectUsersName;
      this.ruleForm.userIds = data.selectUsers
      this.ruleForm.directorName = data.selectUsersName
      this.groupVisible = false
      this.$nextTick(() => {
        this.$refs.ruleForm.clearValidate(['directorName'])
      })
      console.log('225', this.ruleForm);

    },
    handleClose () {
      this.groupVisible = false
      this.groupSaveVisible = false
    },
    // 新增
    rowSave (row, done, loading) {
      console.log(row);
      if (!this.form.attachList) {
        this.form.attachList = [];
      }
      let submitData = {
        name: row.name,
        status: row.status,
        type: row.type,
        userId: row.userId,
        workDesc: row.workDesc,
        progress: row.progress,
        remark: row.remark,
        attachIds: row.attachList,
      };
      if (row.releaseTime) {
        if (Array.isArray(row.releaseTime)) {
          submitData.startDate = row.releaseTime[0];
          submitData.endDate = row.releaseTime[1];
        }
      }
      funList.save(submitData).then(
        async () => {
          this.$message.success(`成功新增`);
          await this.onLoad(this.page, this.query);
          done();
        },
        () => {
          loading();
        }
      );
    },
    // 修改
    rowUpdate (row, index, done, loading) {
      if (!this.form.attachList) {
        this.form.attachList = [];
      }
      let submitData = {
        id: row.id,
        name: row.name,
        status: row.status,
        type: row.type,
        userId: row.userId,
        workDesc: row.workDesc,
        progress: row.progress,
        remark: row.remark,
        attachIds: row.attachList,
      };
      if (row.releaseTime) {
        if (Array.isArray(row.releaseTime)) {
          submitData.startDate = row.releaseTime[0];
          submitData.endDate = row.releaseTime[1];
        }
      }
      funList.update(submitData).then(
        async () => {
          this.$message.success(`成功修改`);
          await this.onLoad(this.page, this.query);
          done();
        },
        () => {
          loading();
        }
      );
    },
    // 删除
    rowDel (row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return funList.remove({
            ids: row.id,
          });
        })
        .then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    // 重置
    searchReset () {
      this.query = {};
      this.onLoad(this.page);
    },
    // 搜索
    searchChange (params, done) {
      this.page.currentPage = 1;
      this.query = params;
      this.onLoad(this.page, params);
      done();
    },
    // 勾选
    selectionChange (list) {
      this.selectionList = list;
    },
    // 重置勾选
    selectionClear () {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    // 多选删除
    handleDelete () {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return funList.remove({
            ids: this.ids,
          });
        })
        .then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    // 查看预览
    // 打开前回调
    async beforeOpen (done, type) {
      if (type === "add") {
        this.form = {};
        done();
        return;
      }
      if (["edit", "view"].includes(type)) {
        funList
          .detail({
            id: this.form.id,
          })
          .then((res) => {
            this.form = res.data.data;
            this.form.fileList = [];
            this.form.releaseTime = [this.form.startDate, this.form.endDate];
            if (res.data.data.attachList) {
              let tempAttachList = [];
              res.data.data.attachList.map((value) => {
                this.form.fileList.push({
                  label: value.originalName,
                  value: value.link,
                });
                tempAttachList.push(value.id);
              });
              this.form.attachList = tempAttachList;
            }
            done();
          });
      }
    },
    // 上传前回调
    uploadBefore (file, done, loading, column) {
      console.log({ file, done, loading, column });
      //文件个数可以在limit属性设置,超过则不会继续上传,也不会走这个函数,这个组件目前只能一次一个个传
      if (validateFile.call(this, "TypeIWP", 10, file)) {
        done();
      } else {
        loading();
      }
    },
    // 上传后执行操作
    uploadAfter (res, done, loading) {
      //fileList是form展示的[{label,value}],attachList是记录每次上传完的id
      if (!res || !res.attachId) {
        this.$message.error("上传失败");
        loading();
      } else {
        this.$message.success("上传成功");

        if (!this.form.attachList) {
          this.form.attachList = [];
          this.form.attachList.push(res.attachId);
        } else {
          this.form.attachList.push(res.attachId);
        }
        done();
      }
    },
    // 删除已上传文件
    uploadDelete (file, column) {
      console.log({ file, column });

      return this.$confirm("是否确定移除该项？").then(() => {
        if (column.prop == "themePictureList") {
          this.form.themePicture = "";
          this.form.themePictureList.splice(file.uid, 1);
        } else {
          this.form.fileList.splice(file.uid, 1);
          this.form.attachList.splice(file.uid, 1);
        }
      });
    },
    // 上传限制
    uploadExceed (limit) {
      this.$message.error(`最多只能上传${limit}个文件`);
    },
    // 预览
    uploadPreview (file, column, done) {
      console.log({ file, column, done });
      // done()
      if (file.type == "img") {
        done()
      } else {
        //url下载
        downloadFileBlob(file.url, file.name)
      }

    },
    // 当前页切换
    currentChange (currentPage) {
      this.page.currentPage = currentPage;
    },
    // 页面显示条数切换
    sizeChange (pageSize) {
      this.page.pageSize = pageSize;
    },
    // 刷新
    refreshChange () {
      this.onLoad(this.page, this.query);
    },
    // 首次加载
    async onLoad (page, params = {}) {
      this.loading = true;
      let query = {
        ...params,
        current: this.page.currentPage,
        size: this.page.pageSize,
      };

      let res = await funList.getGroupList(query);
      if (res && res.data && res.data.success) {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      }
    },
  },
};
</script>
<style scoped lang="scss">
.img-tiny {
  height: 100px;
  width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(245, 247, 250);
}

.btn-group-container {
  display: flex;
}
</style>
