<template>
  <el-row ref="test">
    <el-col :span="5">
      <div class="box">
        <el-scrollbar>
          <basic-container v-loading="treeLoading">
            <avue-tree :option="treeOption" ref="deptTree" :data="treeData" @node-click="nodeClick" node-key="id" :current-node-key="deptId" />
          </basic-container>
        </el-scrollbar>
      </div>
    </el-col>
    <el-col :span="19">
      <basic-container>
        <avue-crud :option="option" :table-loading="loading" :data="data" :page.sync="page" :search.sync="query" :permission="permissionList" v-model="form" ref="crud" :before-open="beforeOpen" :upload-before="uploadBefore" :upload-after="uploadAfter" :upload-delete="uploadDelete" :upload-exceed="uploadExceed" @row-update="rowUpdate" @row-save="rowSave" @row-del="rowDel" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad" class="directSupply-dialog">
          <!-- 左侧菜单按钮 -->
          <template slot="menuLeft">
            <el-button size="small" icon="el-icon-document-add" plain @click="updateStatus(1)">
              发布
            </el-button>
            <el-button size="small" icon="el-icon-document-delete" plain @click="updateStatus(0)">
              取消发布
            </el-button>
            <el-button type="danger" size="small" icon="el-icon-delete" plain @click="handleDelete">
              删除
            </el-button>
          </template>

          <!-- 状态显示 -->
          <template slot-scope="{ row }" slot="status">
            <el-tag type="danger" v-if="row.status === 0">未发布</el-tag>
            <el-tag type="success" v-if="row.status === 1">已发布</el-tag>
          </template>

          <!-- 行操作按钮 -->
          <template slot-scope="{ type, size, row, index }" slot="menu">
            <!-- <el-button :type="type" :size="size" @click.stop="preview(row)">预览</el-button> -->
            <el-button :type="type" :size="size" @click.stop="$refs.crud.rowView(row, index)">
              查看
            </el-button>
            <el-button :type="type" :size="size" @click="$refs.crud.rowEdit(row, index)" v-if="
                row.status === 0
              ">
              编辑
            </el-button>
            <el-button :type="type" :size="size" @click="rowDel(row)" v-if="
                row.status === 0
              ">
              删除
            </el-button>
          </template>
        </avue-crud>
      </basic-container>
    </el-col>
    <Uview :dialogVisible="uviewVisible" :changeVisible="changeUviewVisible" ref="uview" />
  </el-row>
</template>

<script>
import Uview from "@/components/uview/main.vue";
import { mapGetters } from "vuex";
import { validateFile } from "@/views/components/util.js";
import { getDeptTree } from "@/api/infoRelease/partyLead";
import * as funList from "@/api/infoRelease/info";

export default {
  components: {
    Uview,
  },
  data() {
    return {
      uviewVisible: false,
      dialogVisible: false,
      limitCountImg: 1, //上传图片的最大数量
      srcList: [],
      tabLoading: true, //类别loading
      form: {},
      query: {},
      loading: true,
      box: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0,
      },
      type: "",
      // allTypeData: [],
      selectionList: [],
      // viewDialog: false, //详情弹窗
      option: {
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: false,
        index: false,
        selection: true,
        // menuWidth: 210,
        // menuAlign:"center",
        // labelWidth: 120,
        dialogWidth: 1200,
        dialogClickModal: false,
        viewBtn: false,
        viewBtnText: "预览",
        delBtn: false, //默认是有删除、编辑按钮
        editBtn: false,
        dialogCustomClass: "directSupply-dialog",
        column: [
          {
            label: "信息类别",
            prop: "type",
            editDisabled: false,
            dataType: "string",
            width: 100,
            search: true,
            searchSpan: 6,
            span: 24,
            type: "select",
            dicData: [],
            // dicUrl: `/api/user/informationColumn/typeSelect?id=${this.columnId}`,
            props: {
              //对应select的属性
              label: "name",
              value: "id",
            },
            filterable: true,
            rules: [
              {
                required: true,
                message: "请选择信息类别",
                trigger: ["blur", "change"],
              },
            ],
          },
          {
            label: "信息标题",
            prop: "title",
            searchSpan: 6,
            span: 24,
            search: true,
            maxlength: 100,
            // showWordLimit: true,
            rules: [
              {
                required: true,
                whitespace: true,
                message: "请输入标题",
                trigger: ["blur", "change"],
              },
            ],
          },

          {
            label: "内容摘要",
            prop: "abstractInfo",
            hide: true,
            span: 24,
            maxlength: 100,
            // showWordLimit: true,
          },
          {
            label: "发布人",
            prop: "releaseUserName",
            width: 100,
            addDisplay: false, //表单新增时是否可见
            editDisplay: false,
            viewDisplay: false,
          },
          // {
          //   label: "数据状态",
          //   prop: "dataStatusText",
          //   width: 100,
          //   addDisplay: false, //表单新增时是否可见
          //   editDisplay: false,
          //   viewDisplay: false,
          // },
          {
            label: "发布时间",
            prop: "releaseTime",
            width: 180,
            slot: true,
            search: true, //允许高级查询
            addDisplay: false, //表单新增时是否可见
            editDisplay: false,
            viewDisplay: false,
            searchSpan: 8,
            type: "datetime",
            searchRange: true,
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
          },
          {
            label: "发布状态",
            width: 80,
            prop: "status",
            type: "select",
            slot: true,
            search: true, //允许高级查询
            addDisplay: false, //表单新增时是否可见
            editDisplay: false,
            viewDisplay: false,
            searchSpan: 6,
            dicUrl: `/api/blade-system/dict/dictionary?code=publishStatus`,
            props: {
              //对应select的属性
              label: "dictValue",
              value: "dictKey",
            },
          },
          {
            type: "ueditor",
            label: "内容详情",
            prop: "content",
            component: "AvueUeditor",
            // options: {
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            customConfig: {
              excludeMenus: [
                "code",
                "todo",
                "fontName",
                "video",
                "table",
                "source",
                "fullScreen",
              ],
              uploadImgMaxLength: 1, //限制单次图片上传张数
            }, //wangEditor编辑的配置
            propsHttp: {
              res: "data",
              url: "link",
            },
            // },
            hide: true,
            span: 24,
            showColumn: false, //不出现显隐面板中
          },
          {
            label: "主题图片",
            prop: "themePictureList",
            dataType: "object",
            slot: true,
            type: "upload",
            // listType: "picture",
            limit: 1,
            // accept: ".jpeg,.jpg,.png",
            span: 24,
            propsHttp: {
              res: "data",
              url: "link",
              name: "originalName",
            },
            tip: "仅支持上传图片,最多只能上传1个文件，单个文件不超过50MB",
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            hide: true,
            showColumn: false, //不出现显隐面板中
          },
          {
            label: "发布范围",
            prop: "deptIds",
            type: "tree",
            dicData: [],
            multiple: true,
            checkStrictly: true,
            dataType: "array",
            hide: true,
            props: {
              label: "title",
              value: "id",
              children: "children",
            },
            rules: [],
          },
          {
            label: "附件",
            prop: "fileList",
            dataType: "object",
            slot: true,
            type: "upload",
            // listType: "picture",
            limit: 10,
            // accept: ".jpeg,.jpg,.png",
            span: 24,
            propsHttp: {
              res: "data",
              url: "link",
              name: "originalName",
            },
            tip: "仅支持上传图片、视频、音频、office,TXT文件,最多只能上传10个文件，单个文件不超过50MB",
            action: "/api/blade-resource/oss/endpoint/put-file-attach",
            hide: true,
            showColumn: false, //不出现显隐面板中
          },
          {
            label: "审核意见",
            prop: "failReason",
            addDisplay: false, //表单新增时是否可见
            editDisplay: true,
            viewDisplay: true,
            hide: true,
            span: 24,
            showColumn: false,
            readonly: true,
            formslot: true,
          },
        ],
      },
      data: [],
      failReason: "",
      // 组织树
      deptId: "",
      treeLoading: false,
      treeOption: {
        nodeKey: "id",
        addBtn: false,
        menu: false,
        size: "small",
        props: {
          label: "title",
          value: "id",
          children: "children",
        },
      },
      treeData: [],
      columnId: "",
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    ids() {
      let ids = [];
      this.selectionList.forEach((ele) => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.columnId = this.$route.params.id;
    // console.log("初始化",this.$store.getters.tagList)
    // console.log(funList);
    // this.treeLoading = true
    this.initTreeData()
    this.initData(this.columnId)
  },
  watch: {
    $route(to, from) {
      if (to.fullPath !== from.fullPath) {
        this.columnId = to.params.id;
        this.initData(this.columnId)
        this.searchReset()
      }
    },
  },
  methods: {
    initData(id) {
      funList.informationSelect(id).then(res => {
        const column = this.findObject(this.option.column, "type");
        column.dicData = res.data.data;
      });
    },
    // 发布
    async updateStatus(status) {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (
        this.selectionList.findIndex((item) => item.status === 1) !== -1 &&
        status === 1
      ) {
        this.$message.warning("存在已发布的数据,已发布的数据无法再次发布");
        return;
      }
      if (
        this.selectionList.findIndex((item) => item.status === 0) !== -1 &&
        status === 0
      ) {
        this.$message.warning("存在未发布的数据,未发布的数据无法取消发布");
        return;
      }
      let res = null;
      if (status) {
        res = await funList.release({ ids: this.ids });
      } else {
        res = await funList.unRelease({ ids: this.ids });
      }
      if (res && res.data && res.data.success) {
        this.$message.success(`成功${status === 1 ? "发布" : "取消发布"}`);
        this.onLoad(this.page, this.query);
      }
    },
    //Uview预览 start
    showUview() {
      this.uviewVisible = true;
      this.$refs.uview.sendMessage(this.form.title, this.form.content);
    },
    changeUviewVisible() {
      this.uviewVisible = false;
    },
    // 详情弹窗
    preview(row) {
      this.form = row;
      this.queryDetail();
    },
    // 新增
    rowSave(row, done, loading) {
      if (!this.form.attachList) {
        this.form.attachList = [];
      }
      let submitData = {
        columnId: this.columnId,
        title: row.title,
        type: row.type,
        abstractInfo: row.abstractInfo,
        content: row.content,
        themePicture: row.themePicture,
        attachList: row.attachList,
        isColumn: true,
        deptIds: row.deptIds,
      };
      funList.save(submitData).then(
        async () => {
          this.$message.success(`成功新增`);
          await this.onLoad(this.page, this.query);
          done();
        },
        () => {
          loading();
        }
      );
    },
    // 修改
    rowUpdate(row, index, done, loading) {
      if (!this.form.attachList) {
        this.form.attachList = [];
      }
      let submitData = {
        columnId: this.columnId,
        id: row.id,
        title: row.title,
        type: row.type,
        abstractInfo: row.abstractInfo,
        content: row.content,
        themePicture: row.themePicture,
        attachList: row.attachList,
        isColumn: true,
        deptIds:row.deptIds
      };
      funList.update(submitData).then(
        async () => {
          this.$message.success(`成功修改`);
          await this.onLoad(this.page, this.query);
          done();
        },
        () => {
          loading();
        }
      );
    },
    // 删除
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return funList.remove({
            ids: row.id,
          });
        })
        .then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
        });
    },
    // 重置
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
      this.$refs.deptTree.setCurrentKey()
    },
    // 搜索
    searchChange(params, done) {
      this.page.currentPage = 1;
      const deptId = this.query.deptId
      this.query = params;
      this.query.deptId = deptId
      this.onLoad(this.page, params);
      done();
    },
    // 勾选
    selectionChange(list) {
      this.selectionList = list;
    },
    // 重置勾选
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    // 多选删除
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (this.selectionList.findIndex((item) => item.status === 1) !== -1) {
        this.$message.warning("存在已发布的数据,已发布的数据无法删除");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          return funList.remove({
            ids: this.ids,
          });
        })
        .then(() => {
          this.onLoad(this.page, this.query);
          this.$message({
            type: "success",
            message: "操作成功!",
          });
          this.$refs.crud.toggleSelection();
        });
    },
    // 查看预览
    queryDetail() {
      funList.detail({
        id: this.form.id,
      }).then((res) => {
        this.form = res.data.data;
        this.showUview();
      });
    },
    // 打开前回调
    async beforeOpen(done, type) {
      if (type === "add") {
        this.form = {};
        done();
        return;
      }
      if (["edit", "view"].includes(type)) {
        funList.detail({
          id: this.form.id,
        }).then((res) => {
          this.form = res.data.data;
          this.form.fileList = [];
          this.form.themePictureList = [];
          if (res.data.data.attachList) {
            let tempAttachList = [];
            if (res.data.data.themePicture) {
              this.form.themePictureList = [
                {
                  label: res.data.data.themePicture.originalName,
                  value: res.data.data.themePicture.link,
                },
              ];
              this.form.themePicture = res.data.data.themePicture.id;
            }
            res.data.data.attachList.map((value) => {
              this.form.fileList.push({
                label: value.originalName,
                value: value.link,
              });
              tempAttachList.push(value.id);
            });
            this.form.attachList = tempAttachList;
          }
          done();
        });
      }
    },
    // 上传前回调
    uploadBefore(file, done, loading, column) {
      console.log({ file, done, loading, column });
      //文件个数可以在limit属性设置,超过则不会继续上传,也不会走这个函数,这个组件目前只能一次一个个传
      if (column.prop == "themePictureList") {
        if (validateFile.call(this, "onlyImage", 50, file)) {
          done();
        } else {
          loading();
        }
      } else {
        if (validateFile.call(this, "all", 50, file)) {
          done();
        } else {
          loading();
        }
      }
    },
    // 上传后执行操作
    uploadAfter(res, done, loading, column) {
      //fileList是form展示的[{label,value}],attachList是记录每次上传完的id
      if (!res || !res.attachId) {
        this.$message.error("上传失败");
        loading();
      } else {
        this.$message.success("上传成功");
        if (column.prop == "themePictureList") {
          this.form.themePicture = res.attachId;
          done();
        } else {
          if (!this.form.attachList) {
            this.form.attachList = [];
            this.form.attachList.push(res.attachId);
          } else {
            this.form.attachList.push(res.attachId);
          }
          done();
        }
      }
    },
    // 删除已上传文件
    uploadDelete(file, column) {
      console.log({ file, column });

      return this.$confirm("是否确定移除该项？").then(() => {
        if (column.prop == "themePictureList") {
          this.form.themePicture = "";
          this.form.themePictureList.splice(file.uid, 1);
        } else {
          this.form.fileList.splice(file.uid, 1);
          this.form.attachList.splice(file.uid, 1);
        }
      });
    },
    // 上传限制
    uploadExceed(limit) {
      this.$message.error(`最多只能上传${limit}个文件`);
    },
    // 当前页切换
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    // 页面显示条数切换
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    // 刷新
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    // 首次加载
    async onLoad(page, params = {}) {
      this.loading = true;
      // Object.assign(params, this.query),
      let query = {
        ...params,
        current: this.page.currentPage,
        size: this.page.pageSize,
        columnId: this.columnId,
        isColumn: true,
      };
      if (query.releaseTime) {
        if (Array.isArray(query.releaseTime)) {
          query.startTime  = query.releaseTime[0];
          query.endTime = query.releaseTime[1];
        }
        delete query.releaseTime;
      }
      // console.log(query);
      // this.type !== "全部" ? (query.type = this.type) : null;
      let res = await funList.getList(query);
      if (res && res.data && res.data.success) {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        if (this.page.currentPage > 1 && this.page.currentPage > data.pages) {
          this.page.currentPage = data.pages
          this.onLoad()
        }
        this.selectionClear();
      }
    },

    // filterNodeMethod(value, data) {
    //   if (!value) return true
    //   return data.deptName.indexOf(value.trim()) !== -1
    // },
    nodeClick(data) {
      this.query.deptId = data.id
      this.deptId = data.id
      this.page.currentPage = 1
      this.onLoad(this.page, this.query)
    },
    async initTreeData() {
      this.treeLoading = true
      const Data = (await getDeptTree()).data.data
      this.treeData = Data
      const column = this.findObject(this.option.column, "deptIds");
      column.dicData = Data;
      this.treeLoading = false
    },
  },
};
</script>
<style scoped lang="scss">
.img-tiny {
  height: 100px;
  width: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(245, 247, 250);
}
.btn-group-container {
  display: flex;
}
</style>
