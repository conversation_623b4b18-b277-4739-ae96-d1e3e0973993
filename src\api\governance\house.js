import request from '@/router/axios';


/**
 * @description: 请求列表
 * @param {object} params
 * @author:
 */
export const getList = (params) => {
  return request({
    url: '/api/admin/house/page',
    method: 'get',
    params,
  })
}

/**
 * @description: 请求列表
 * @param {object} params
 * @author:
 */
export const houseSelectList = (params) => {
  return request({
    url: '/api/admin/house/select',
    method: 'get',
    params,
  })
}

/**
 * @description: 新增
 * @param {object} data
 * @author:
 */
export const save = (data) => {
  return request({
    url: '/api/admin/house/save',
    method: 'post',
    data,
  })
}

/**
 * @description: 查看详情
 * @param {object} params
 * @author:
 */
export const detail = (params) => {
  return request({
    url: '/api/admin/house/detail',
    method: 'get',
    params,
  })
}

/**
 * @description: 修改
 * @param {object} data
 * @author:
 */
export const update = (data) => {
  return request({
    url: '/api/admin/house/update',
    method: 'post',
    data,
  })
}



/**
 * @description: 删除
 * @param {object} params
 * @author:
 */
export const remove = (params) => {
  return request({
    url: '/api/admin/house/remove',
    method: 'post',
    params,
  })
}

/**
 * @description: 发送审核
 * @param {object} params
 * @author:
 */
export const sendAudit = (params) => {
  return request({
    url: '/api/admin/house/sendAudit',
    method: 'post',
    params,
  })
}

/**
 * @description: 审核
 * @param {object} params
 * @author:
 */
export const doAudit = (data) => {
  return request({
    url: '/api/admin/house/doAudit',
    method: 'post',
    data,
  })
}

/**
 * @description: 审定
 * @param {object} params
 * @author:
 */
export const doAuthorize = (data) => {
  return request({
    url: '/api/admin/house/doAuthorize',
    method: 'post',
    data,
  })
}
