import request from '@/router/axios';

export const getAppList = (current, size, params) => {
  return request({
    url: '/api/admin/app-center/page',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  })
}
// 应用中心列表
export const getAppCenterList = (current, size, name) => {
  return request({
    url: '/api/user/app-center/list',
    method: 'get',
    params: {
      name,
      current,
      size,
      flag: 1 // 固定筛选web类型
    }
  })
}

export const getAppDet = (id) => {
  return request({
    url: '/api/admin/app-center/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const appSave = (data) => {
  return request({
    url: '/api/admin/app-center/save',
    method: 'post',
    data
  })
}


export const appUpdate = (data) => {
  return request({
    url: '/api/admin/app-center/update',
    method: 'put',
    data
  })
}
export const appDel = (ids) => {
  return request({
    url: '/api/admin/app-center/remove',
    method: 'delete',
    params: {
      ids,
    }
  })
}
export const appOpenAndClose = (id) => {
  return request({
    url: '/api/admin/app-center/open',
    method: 'put',
    params: {
      id,
    }
  })
}
// 收藏
export const isCollect = (type, id) => {
  if (type) {
    return request({
      url: '/api/user/app-center/collect',
      method: 'put',
      params: {
        id,
      }
    })
  } else {
    return request({
      url: '/api/user/app-center/cancelCollect',
      method: 'put',
      params: {
        id,
      }
    })
  }

}

