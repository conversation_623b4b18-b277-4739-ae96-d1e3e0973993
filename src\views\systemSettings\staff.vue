<!--
 * @Description 系统设置=人员配置
 * @Author: linzq33
 * @Date: 2025-07-18 17:00:10
 * @LastEditors: 
 * @LastEditTime: 
-->
<template>
  <div>
    <basic-container>
      <avue-tabs :option="tabsOption" @change="handleTabChange"></avue-tabs>
      <avue-crud :option="option" :table-loading="loading" :data="data" :search.sync="search"
        :permission="permissionList" v-model="form" ref="crud" @search-change="searchChange" @search-reset="searchReset"
        @refresh-change="refreshChange" @on-load="onLoad" @tree-load="treeLoad" @selection-change="selectionChange">
        <template slot="menuLeft">
          <el-button type="primary" size="small" icon="el-icon-plus" @click="handleAdd">新 增
          </el-button>
          <el-button type="danger" size="small" icon="el-icon-delete" @click="handleDel">批量删除
          </el-button>
        </template>
        <template slot-scope="{type,size,row,index}" slot="menu">
          <el-button :size="size" :type="type" icon="el-icon-edit" @click="handleUpdate(row, index)">编 辑</el-button>
          <el-button v-if="row.configureId" :size="size" :type="type" icon="el-icon-delete"
            @click="handleDel(row, index)">删 除</el-button>
        </template>
        <!--  -->
      </avue-crud>
    </basic-container>
    <el-dialog :visible.sync="dialogVisible" append-to-body="true" :close-on-click-modal="false" top="100px" width="30%"
      @close="close()" :fullscreen="isFullscreen">
      <div slot="title" class="header">
        <div class="avue-crud__dialog__header">
          <span class="el-dialog__title">{{ dialogTitle }}</span>
          <div class="avue-crud__dialog__menu">
            <i @click="isFullScreen" class="el-dialog__close el-icon-full-screen"></i>
          </div>
        </div>
      </div>
      <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-form-item v-if="dialogTitle == '新增'" label="机构名称" prop="deptName">
          <el-cascader v-model="ruleForm.deptName" style="width: 100%;" :options="organOptions" size="small"
            :props="organProps" filterable @change="getUserList" :before-filter="beforeFilter"></el-cascader>
        </el-form-item>
        <el-form-item v-if="type.prop != 3" label="处理人" prop="handler">
          <el-cascader v-model="ruleForm.handler" style="width: 100%;" :options="handlerOptions" size="small"
            :props="handlerProps" :placeholder="dialogTitle == '新增' ? '请先选择机构后再选择处理人' : '请选择'"></el-cascader>
        </el-form-item>
        <el-form-item v-else label="审核人员" prop="handler">
          <el-cascader v-model="ruleForm.handler" style="width: 100%;" :options="handlerOptions" size="small"
            :props="handlerProps" :placeholder="dialogTitle == '新增' ? '请先选择机构后再选择审核人员' : '请选择'"></el-cascader>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="close()" size="small" :loading="btnLoading">取 消</el-button>
        <el-button type="primary" @click="save" size="small" :loading="btnLoading">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getOrgantree, getList, remove, add, update, getUserList } from "@/api/systemSetting/staff";
import { mapGetters } from "vuex";

let type = { prop: 1 } //选项卡参数
export default {
  components: {},
  props: {
  },
  data () {
    return {
      type,
      tabsOption: {
        column: [
          { label: "民情处理人", prop: "1" },
          { label: "随手拍处理人", prop: "2" },
          { label: "招工信息审核人员", prop: "3" },
        ],
      },
      parentId: 0,
      organParentId: 0,
      userParentId: 0,
      form: {},
      query: {},
      loading: false,
      selectionList: [],
      data: [],
      option: {
        index: true,
        indexLabel: "序号",
        lazy: true,
        height: "auto",
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        selection: true,
        addBtn: false,
        viewBtn: false,
        dicFlag: true,
        searchLabelWidth: 90,
        dialogWidth: '30%',
        column: [
          { label: "机构名称", prop: "deptName", search: true, span: 24 },
          { label: "姓名", prop: "realName", span: 24 },
          { label: "手机号", prop: "phone", editDisabled: false },
        ],
      },
      isFullscreen: false,
      dialogVisible: false,
      dialogTitle: '新增',
      ruleForm: {
        deptName: []
      },
      rules: {
        deptName: [{ required: true, message: '请选择机构', trigger: 'blur' }],
        handler: [{ required: true, message: '请选择处理人', trigger: 'blur' }],
      },
      organOptions: [],
      organProps: {
        lazy: true,
        label: 'deptName',
        lazyLoad (node, resolve) {
          if (node.level != 0) {
            let nodes = []
            getOrgantree(node.value || 0, { type: type.prop }).then(res => {
              nodes = res.data.data
              setTimeout(() => {
                for (let i = 0; i < nodes.length; i++) {
                  nodes[i].leaf = !nodes[i].hasChildren
                }
                resolve(nodes);
              }, 100);
            })
          }
        }
      },
      handlerProps: {
        label: 'realName',
        value: 'id',
      },
      handlerOptions: [],
      btnLoading: false,

    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList () {
      return {
        addBtn: false,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
      };
    },
    ids () {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.configureId);
      });
      return ids.join(",");
    },
  },
  watch: {
    type: function (newVal) {
      type = newVal
    }
  },
  created () {
    // 获取组织树
    getOrgantree(this.organParentId, { type: this.type.prop }).then(res => {
      this.organOptions = res.data.data
      for (let i = 0; i < this.organOptions.length; i++) {
        this.organOptions[i].leaf = !this.organOptions[i].hasChildren
      }
    })
  },
  methods: {
    handleAdd () {
      this.dialogTitle = '新增';
      this.dialogVisible = true;
      this.$refs.ruleForm.resetFields()
      this.ruleForm = {}
    },
    getUserList (val) {
      console.log('188', val);
      const deptId = val[val.length - 1]
      getUserList(this.userParentId, { type: this.type.prop, deptId }).then(res => {
        this.handlerOptions = res.data.data
      })
    },
    beforeFilter (val) {
      getOrgantree('', { type: this.type.prop, deptName: val }).then(res => {
        this.organOptions = res.data.data
        for (let i = 0; i < this.organOptions.length; i++) {
          this.organOptions[i].leaf = !this.organOptions[i].hasChildren
        }
      })

      return false
    },
    save () {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          if (this.dialogTitle == '新增') {
            const obj = {
              userId: this.ruleForm.handler[0],
              deptId: this.ruleForm.deptName[this.ruleForm.deptName.length - 1],
              type: this.type.prop,
            }
            this.btnLoading = true
            add(obj).then(() => {
              this.$message({
                type: "success",
                message: "操作成功!"
              });
              // 数据回调进行刷新
              this.$refs.crud.refreshTable();
              this.onLoad();
              this.close()
            }, error => {
              window.console.log(error);
              loading();
            }).finally(() => {
              this.btnLoading = false
            });;
          } else {
            const obj = {
              userId: this.ruleForm.handler[0],
              deptId: this.ruleForm.value,
              type: this.type.prop,
            }
            this.btnLoading = true
            update(obj).then(() => {
              this.$message({
                type: "success",
                message: "操作成功!"
              });
              // 数据回调进行刷新
              this.$refs.crud.refreshTable();
              this.onLoad();
              this.close()
            }, error => {
              window.console.log(error);
              loading();
            }).finally(() => {
              this.btnLoading = false
            });
          }


        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    handleUpdate (row, index) {
      this.dialogVisible = true;
      this.dialogTitle = '编辑';
      // this.$refs.ruleForm.resetFields()
      this.getUserList([row.value])
      this.ruleForm = row
      this.ruleForm.handler = row.userId
    },

    isFullScreen () {
      this.isFullscreen = !this.isFullscreen;
    },
    close () {
      this.dialogVisible = false;
      this.ruleForm = {};
    },
    /**
     * @description: 选项卡改变
     * @param {object} column 选择项
     * @author: wangyy553
     */
    handleTabChange (column) {
      this.type = column;
      this.$refs.crud.searchReset();
    },
    searchChange (params, done) {
      this.query = params;
      this.parentId = '';
      this.onLoad(params);
      done();
    },
    searchReset () {
      this.query = {};
      this.parentId = 0;
      this.onLoad();
    },
    selectionChange (list) {
      this.selectionList = list;
    },
    selectionClear () {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    refreshChange () {
      this.onLoad(this.query);
    },

    handleDel (row, index) {
      if (index !== undefined) {
        this.$confirm('是否删除此数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          remove(row.configureId).then(() => {
            this.$message.success('删除成功');
            this.$refs.crud.refreshTable();
            this.onLoad();
          })
        })
      } else {
        if (this.ids === '') {
          this.$message.warning('请至少选择一项数据删除');
          return false;
        }
        this.$confirm('是否删除该机构的处理人信息数据?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          remove(this.ids).then(() => {
            this.$message.success('删除成功');
            this.$refs.crud.refreshTable();
            this.$refs.crud.toggleSelection();
            // 表格数据重载
            this.onLoad();
          })
        })
      }
    },

    onLoad (params = {}) {
      this.loading = true;
      var obj = Object.assign(params, this.query);
      obj.type = this.type.prop || "1";
      getList(this.parentId, obj).then((res) => {
        this.data = res.data.data
        this.loading = false;
      });
    },
    treeLoad (tree, treeNode, resolve) {
      const parentId = tree.id;
      getList(parentId, { type: this.type.prop }).then(res => {
        resolve(res.data.data);
      });
    },
  },
};
</script>

<style lang='scss' scoped>
.tip-container {
  background: rgb(235, 245, 255);
  padding: 10px 30px;
  // height: 50px;
  border: 1px solid rgb(140, 197, 255);
  border-radius: 5px;
  color: #909399;
  margin-bottom: 20px;
}
</style>
