<template>
  <div>
    <basic-container v-if="!showServiceForm">
      <!-- 列表部分 -->
      <avue-crud :option="option" :table-loading="loading" :data="data" ref="crud" v-model="form" :page.sync="page" :permission="permissionList" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange" @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
        <template slot="menuLeft">
          <el-button size="small" type="primary" icon="el-icon-setting" @click="showServiceForm = true">新增服务工单
          </el-button>
        </template>
        <template slot="menuRight">
        </template>
        <template #menu="scope">
          <el-button type="text" icon="el-icon-view" size="small" @click="viewServiceForm(scope.row)">查 看
          </el-button>
          <el-button v-if="scope.row.status === 0" type="text" icon="el-icon-edit" size="small" @click="editServiceForm(scope.row)">编 辑
          </el-button>
          <el-button v-if="scope.row.status === 0" type="text" icon="el-icon-delete" size="small" @click="rowDel(scope.row)">删 除
          </el-button>
        </template>
        <template slot-scope="{row}" slot="menuSetting">
          <span v-if="row.menuSetting">已配置</span>
          <span v-else style="color:#FC474C">待配置</span>
        </template>
        <template slot-scope="{row}" slot="systemSetting">
          <span v-if="row.systemSetting">已配置</span>
          <span v-else style="color:#FC474C">待配置</span>
        </template>
        <template slot-scope="{row}" slot="status">
          <el-tag v-if="row.status === 1" type="success">已完成</el-tag>
          <el-tag v-else type="danger">进行中</el-tag>
        </template>
      </avue-crud>
    </basic-container>
    <!-- 服务工单表单部分 -->
    <div v-if="showServiceForm" class="service-form-container">
      <div class="service-steps">
        <div class="steps-container" :style="{'--progress': activeStep}">
          <div class="step-item" :class="{active: activeStep >= 0}">
            <div class="step-icon">
              <img v-if="activeStep >= 0" src="@/assets/provisioning/step_one_active.png" alt="开通租户">
              <img v-else src="@/assets/provisioning/step_one.png" alt="开通租户">
            </div>
            <div class="step-text">开通租户</div>
          </div>
          <div class="step-item" :class="{active: activeStep >= 1}">
            <div class="step-icon">
              <img v-if="activeStep >= 1" src="@/assets/provisioning/step_two_active.png" alt="功能超市">
              <img v-else src="@/assets/provisioning/step_two.png" alt="功能超市">
            </div>
            <div class="step-text">功能超市</div>
          </div>
          <div class="step-item" :class="{active: activeStep >= 2}">
            <div class="step-icon">
              <img v-if="activeStep >= 2" src="@/assets/provisioning/step_three_active.png" alt="系统初始化">
              <img v-else src="@/assets/provisioning/step_three.png" alt="系统初始化">
            </div>
            <div class="step-text">系统初始化</div>
          </div>
          <div class="step-item" :class="{active: activeStep >= 3}">
            <div class="step-icon">
              <img v-if="activeStep >= 3" src="@/assets/provisioning/step_four_active.png" alt="配置说明">
              <img v-else src="@/assets/provisioning/step_four.png" alt="配置说明">
            </div>
            <div class="step-text">配置说明</div>
          </div>
        </div>
      </div>

      <!-- 步骤组件 -->
      <basic-container>
        <div class="step-content-wrapper">
          <tenant-setup v-if="activeStep === 0" ref="tenantSetup" :is-view-mode="isViewMode" :form-data="viewFormData" @cancel="cancelForm" @save="saveForm" @next="nextStep" />
        </div>
        <div class="step-content-wrapper">
          <function-market v-if="activeStep === 1" ref="functionMarket" :is-view-mode="isViewMode" :form-data="viewFormData" @cancel="cancelForm" @save="saveForm" @prev="prevStep" @next="nextStep" />
        </div>
        <system-initialization v-if="activeStep === 2" ref="systemInitialization" :is-view-mode="isViewMode" :form-data="viewFormData" @cancel="cancelForm" @save="saveForm" @prev="prevStep" @next="nextStep" @submit="submitForm" />

        <configuration-guide v-if="activeStep === 3" ref="configurationGuide" :is-view-mode="isViewMode" :form-data="viewFormData" @cancel="cancelForm" @prev="prevStep" />
      </basic-container>
    </div>

    <!-- 短信验证码弹出框 -->
    <el-dialog title="短信验证码" v-loading="true" :visible.sync="smsDialogVisible" width="380px" append-to-body :close-on-click-modal="false" :close-on-press-escape="false" :show-close="false" center custom-class="sms-verification-dialog">
      <div class="sms-dialog-content">
        <div class="phone-info">
          <span>验证码将发送至：</span>
          <span class="phone-number">{{ phoneNumber }}</span>
        </div>

        <el-form ref="smsForm" :model="smsForm" :rules="smsRules" label-width="0">
          <el-form-item prop="code">
            <div class="code-input-container">
              <el-input v-for="(item, index) in 6" :key="index" v-model="codeInputs[index]" :ref="`codeInput${index}`" maxlength="1" class="code-input-item" @input="handleCodeInput(index, $event)" @keydown.native="handleKeydown(index, $event)">
              </el-input>
            </div>
          </el-form-item>
        </el-form>

        <div class="resend-section">
          <span v-if="countdown > 0" class="countdown-text">
            {{ countdown }}秒后可重新发送
          </span>
          <el-button v-else type="text" @click="resendSms" class="resend-btn">
            重新发送
          </el-button>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelSms" :disabled="smsLoading">取消</el-button>
        <el-button type="primary" @click="send" :disabled="this.smsForm.code.length != 6" :loading="smsLoading">确认</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getList,
  getDetail,
  remove,
  update,
  add,
  platformOpen
  // validCode,
} from "@/api/provisioning/index";
import { mapGetters } from "vuex";
import { validCode } from "@/api/provisioning/index";
import { encrypt } from "@/util/sm2";

// 导入子组件
import TenantSetup from './components/TenantSetup.vue';
import FunctionMarket from './components/FunctionMarket.vue';
import SystemInitialization from './components/SystemInitialization.vue';
import ConfigurationGuide from './components/ConfigurationGuide.vue';

export default {
  components: {
    TenantSetup,
    FunctionMarket,
    SystemInitialization,
    ConfigurationGuide
  },
  data() {
    return {
      form: {},
      selectionList: [],
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: false,
        index: true,
        selection: false,
        addBtn: false,
        viewBtn: false,
        editBtn: false,
        menuWidth: 380,
        dialogWidth: 900,
        dialogClickModal: false,
        delBtn: false,
        column: [
          {
            label: "租户名称",
            prop: "regionName",
            search: true,
          },
          {
            label: "功能超市",
            prop: "menuSetting",
          },
          {
            label: "系统初始化",
            prop: "systemSetting",
          },
          {
            label: "状态",
            prop: "status",
            search: true,
            dicUrl: "/api/blade-system/dict/dictionary?code=service_active_status",
            type: "select",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
          },
          {
            label: "创建人",
            prop: "createUserAccount",
          },
          {
            label: "创建时间",
            prop: "createTime",
          },
          {
            label: "最后更新时间",
            prop: "updateTime",
          },
        ]
      },
      data: [],
      showServiceForm: false,
      isViewMode: false,
      viewFormData: {},
      activeStep: 0,
      smsDialogVisible: false,
      phoneNumber: '',
      countdown: 0,
      timer: null,
      smsLoading: false,
      smsForm: {
        code: ''
      },
      smsRules: {
        // code: [
        //   { required: true, message: '请输入验证码', trigger: 'blur' },
        // ]
      },
      codeInputs: ['', '', '', '', '', '']
    };
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.provisioning_add, false),
        viewBtn: this.vaildData(this.permission.provisioning_view, false),
        delBtn: this.vaildData(this.permission.provisioning_delete, false),
        editBtn: this.vaildData(this.permission.provisioning_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    rowDel(row) {
      this.$confirm("删除后所填数据无法恢复，是否继续?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return remove(row.id);
      }).then(() => {
        this.onLoad(this.page);
        this.$message.success("操作成功!");
      });
    },

    searchReset() {
      this.query = {};
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },

    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query))
        .then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.selectionClear();
        })
        .finally(() => {
          this.loading = false;
          this.$refs.crud.refreshTable()
        });
    },

    cancelForm() {
      this.showServiceForm = false;
      this.isViewMode = false;
      this.viewFormData = {};
      this.activeStep = 0;
    },

    nextStep(data) {
      // console.log(data)
      this.viewFormData = data
      if (this.activeStep < 3) {
        this.activeStep++;
      }
    },

    prevStep(data) {
      this.viewFormData = data
      if (this.activeStep > 0) {
        this.activeStep--;
      }
    },

    submitForm(data) {
      console.log(data,"submitForm data")
      this.viewFormData = data
      if (!this.viewFormData.regionCode || !this.viewFormData.adminName || !this.viewFormData.contactNumber) {
        this.$message.warning("请填写完整信息")
        this.activeStep = 0
        return
      }
      if (!this.viewFormData.webMenuId || !this.viewFormData.appMenuId) {
        this.$message.warning("请选择对应菜单")
        this.activeStep = 1
        return
      }

      // 显示短信验证码弹框
      this.showSmsDialog(this.viewFormData.contactNumber);
    },
    send() {
      // console.log(this.viewFormData,"send this.viewFormData")
      // return
      this.smsLoading = true
      const formData = new FormData();
      formData.append('regionCode', this.viewFormData.regionCode);
      if(this.viewFormData.id)formData.append('id', this.viewFormData.id);
      formData.append('adminName', this.viewFormData.adminName);
      formData.append('contactNumber', this.viewFormData.contactNumber);
      if(this.viewFormData.address)formData.append('address', this.viewFormData.address);
      formData.append('webMenuIds', this.viewFormData.webMenuId);
      formData.append('appMenuIds', this.viewFormData.appMenuId);
      formData.append('smsKey', this.viewFormData.smsKey);
      formData.append('smsCode', this.viewFormData.smsCode);
      if (this.viewFormData.userDeptFileList && this.viewFormData.userDeptFileList.length > 0) formData.append('userDeptFile', this.viewFormData.userDeptFileList[0].raw);
      if (this.viewFormData.villageTownFileList && this.viewFormData.villageTownFileList.length > 0) formData.append('villageTownFile', this.viewFormData.villageTownFileList[0].raw);
      // console.log(formData,"formData")
      // return
      platformOpen(formData).then((res) => {
        this.viewFormData.id = res.data.data
        this.cancelSms()
        this.$message.success("操作成功!");
        this.activeStep = 3
        this.smsLoading = false
      }).catch(error => {
        this.cancelSms()
        console.log(error);
      }).finally(() => {
        this.smsLoading = false
      })
    },

    saveForm(data, type) {
      // console.log(data, "data", this.viewFormData, "this.viewFormData")
      this.viewFormData = data
      const formData = new FormData();
      formData.append('regionCode', data.regionCode);
      if (data.adminName) formData.append('adminName', data.adminName);
      if (data.contactNumber) formData.append('contactNumber', data.contactNumber);
      if (data.address != null) formData.append('address', data.address);
      if (data.webMenuId) formData.append('webMenuIds', data.webMenuId);
      if (data.webMenuId) formData.append('appMenuIds', data.appMenuId);
      // }
      if (type == "systemInitialization") {
        if (data.villageTownFileList.length > 0) formData.append('villageTownFile', data.villageTownFileList[0].raw);
        //  console.log(data.userDeptFileList[0], "userDeptFileList")
        if (data.userDeptFileList.length > 0) formData.append('userDeptFile', data.userDeptFileList[0].raw);
      }
      if (!this.viewFormData.id) {
        add(formData).then((res) => {
          this.$message.success("操作成功!");
          this.viewFormData.id = res.data.data
          console.log(res);
        }).catch(error => {
          console.log(error);
        });
      } else {
        formData.append('id', this.viewFormData.id);
        update(formData).then(() => {
          this.$message.success("操作成功!");
        }).catch(error => {
          console.log(error);
        });

      }

    },

    verifyPhone() {
      validCode(encrypt(this.viewFormData.contactNumber)).then(res => {
        console.log('验证码发送结果:', res);
        if (res.data.code === 200) {
          console.log(res, "")
          this.viewFormData.smsKey = res.data.data;
          this.$message.success('验证码已发送');
          this.startCountdown();
        }
      });
    },

    viewServiceForm(row) {
      console.log(row)
      getDetail(row.id).then(res => {
        this.viewFormData = res.data.data;
        this.isViewMode = true;
        this.showServiceForm = true;
        this.activeStep = 0;
      });
    },

    editServiceForm(row) {
      getDetail(row.id).then(res => {
        this.viewFormData = res.data.data;
        this.isViewMode = false;
        this.showServiceForm = true;
        this.activeStep = 0;
      });
    },
    // 显示短信验证码弹框
    showSmsDialog(phone) {
      this.phoneNumber = phone;
      this.smsDialogVisible = true;
      this.verifyPhone()
    },

    // 开始倒计时
    startCountdown() {
      this.countdown = 60;
      this.timer = setInterval(() => {
        this.countdown--;
        if (this.countdown <= 0) {
          clearInterval(this.timer);
          this.timer = null;
        }
      }, 1000);
    },

    // 重新发送验证码
    resendSms() {
      // 调用发送短信的API
      validCode(encrypt(this.phoneNumber)).then(res => {
        if (res.data.code === 200) {
          this.viewFormData.smsKey = res.data.data;
          this.$message.success('验证码已重新发送');
          this.startCountdown();
        } else {
          this.$message.error(res.data.data.msg);
        }
      });
    },

    // 取消验证码
    cancelSms() {
      this.smsDialogVisible = false;
      this.resetCodeInputs();
      if (this.timer) {
        clearInterval(this.timer);
        this.timer = null;
      }
      this.countdown = 0
    },
    handleCodeInput(index, value) {
      // 只允许数字
      const numValue = value.replace(/[^0-9]/g, '');
      this.codeInputs[index] = numValue;

      // 更新完整验证码
      this.smsForm.code = this.codeInputs.join('');
      this.viewFormData.smsCode = this.smsForm.code
      // 自动跳转到下一个输入框
      if (numValue && index < 5) {
        this.$nextTick(() => {
          this.$refs[`codeInput${index + 1}`][0].focus();
        });
      }

      // 如果输入完成，自动验证
      // if (this.smsForm.code.length === 6) {
      //   this.$refs.smsForm.validateField('code');
      // }
    },
    handleKeydown(index, event) {
      // 退格键处理
      console.log(event.key)
      if (event.key === 'Backspace' && !this.codeInputs[index] && index > 0) {
        this.$refs[`codeInput${index - 1}`][0].focus();
      }
      // 左右箭头键处理
      if (event.key === 'ArrowLeft' && index > 0) {
        this.$refs[`codeInput${index - 1}`][0].focus();
      }
      if (event.key === 'ArrowRight' && index < 5) {
        this.$refs[`codeInput${index + 1}`][0].focus();
      }
    },
    resetCodeInputs() {
      this.codeInputs = ['', '', '', '', '', ''];
      this.smsForm.code = '';
      this.viewFormData.smsCode = ""
    }
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  }
};
</script>

<style lang="scss" scoped>
/* 新增的服务表单容器样式 */
.service-form-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 步骤内容容器 */
.step-content-wrapper {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 重要：防止内容溢出 */
}

/* 步骤组件统一高度 */
.step-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden; /* 防止内容溢出 */

  /* 内容区域 */
  .step-content-area {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 20px;
  }
}
.service-steps {
  background-color: #e6f1fc;
  border-radius: 8px;
  padding: 25px;
  // margin-bottom: 10px;
  background-image: url("~@/assets/provisioning/bg_out.png");
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
}

.steps-container {
  background-color: #f5faff;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  justify-content: space-around;
  background-image: url("~@/assets/provisioning/bg_in.png");
  background-repeat: no-repeat;
  background-position: center;
  position: relative;
  border: 1px solid #fff;
}

.steps-container::before {
  content: "";
  position: absolute;
  top: 80px;
  left: calc(12.5% - 25px);
  right: calc(12.5% - 25px);
  height: 3px;
  background-color: #d9d9d9;
  z-index: 1;
  border-radius: 20px;
}

.steps-container::after {
  content: "";
  position: absolute;
  top: 80px;
  left: calc(12.5% - 25px);
  width: calc((100% - 25% + 50px) * (var(--progress) + 1) / 4);
  height: 3px;
  background-color: #409eff;
  z-index: 2;
  transition: width 0.3s ease;
  border-radius: 20px;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 25%;
  position: relative;
  z-index: 3;
}

.step-icon {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  position: relative;
  z-index: 4;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }
}

.step-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-top: 15px;
}

.step-item.active .step-text {
  color: #409eff;
  font-weight: 600;
}

.form-title {
  display: flex;
  align-items: center;

  .title-bar {
    width: 4px;
    height: 20px;
    background-color: #409eff;
    margin-right: 8px;
  }

  span {
    font-size: 16px;
    font-weight: 500;
  }
}

.title-divider {
  height: 1px;
  background-color: #e4e7ed;
  margin: 15px 0 20px 0;
}

.actions-divider {
  height: 1px;
  background-color: #e4e7ed;
  margin: 20px -20px 20px -20px;
}

.action-buttons {
  display: flex;
  justify-content: end;
  align-items: center;
  width: 100%;

  .step-buttons {
    display: flex;
    gap: 10px;

    .el-button {
      min-width: 80px;
    }
  }
}

::v-deep .sms-verification-dialog {
  border-radius: 8px;

  .el-dialog__header {
    padding: 16px 20px 12px;
    border-bottom: 1px solid #f0f2f7;

    .el-dialog__title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
    }
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 12px 20px 16px;
    border-top: 1px solid #f0f2f7;
  }
}

.sms-dialog-content {
  text-align: center;

  .phone-info {
    margin-bottom: 20px;
    font-size: 14px;
    color: #666;

    .phone-number {
      color: #409eff;
      font-weight: 500;
    }
  }

  .code-input-container {
    display: flex;
    justify-content: center;
    gap: 8px;
    margin-bottom: 16px;

    .code-input-item {
      width: 40px;

      ::v-deep .el-input__inner {
        width: 40px;
        height: 40px;
        text-align: center;
        font-size: 18px;
        font-weight: 500;
        border-radius: 6px;
        border: 1px solid #dcdfe6;
        padding: 0;

        &:focus {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
        }

        &:hover {
          border-color: #c0c4cc;
        }
      }
    }
  }

  .resend-section {
    height: 24px;
    display: flex;
    justify-content: center;
    align-items: center;

    .countdown-text {
      color: #909399;
      font-size: 13px;
    }

    .resend-btn {
      color: #409eff;
      font-size: 13px;
      padding: 0;
      height: auto;

      &:hover {
        color: #66b1ff;
      }
    }
  }
}

.dialog-footer {
  text-align: center;

  .el-button {
    min-width: 72px;
    height: 36px;
    border-radius: 6px;
    font-size: 14px;

    & + .el-button {
      margin-left: 12px;
    }
  }
}
</style>
