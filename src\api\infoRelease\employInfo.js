/*
 * @Descrip: 用工信息接口
 * @Author: linzq33
 * @Date: 2025-07-25
 * @LastEditors: 
 * @LastEditTime: 
 */
import request from '@/router/axios';

/**
 * @description 获取劳务用工列表
 * @param {number} current
 * @param {number} size
 * @param {object} params
 */
export const getList = (current, size, params) => {
  return request({
    url: '/api/blade-labor/labor/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}


/**
 * @description 获取用工详情
 * @param {number} id
 */
export const getDetail = (id) => {
  return request({
    url: '/api/blade-labor/labor/detail',
    method: 'get',
    params: {
      id
    }
  })
}


/**
 * @description 劳务用工批量上移
 * @param {string} ids
 */
// export const up = (ids) => {
//   return request({
//     url: '/api/blade-labor/labor/up',
//     method: 'post',
//     params: {
//       ids,
//     }
//   })
// }

/**
 * @description 劳务用工批量下架
 * @param {string} ids
 */
export const down = (ids) => {
  return request({
    url: '/api/blade-labor/labor/down',
    method: 'post',
    params: {
      ids,
    }
  })
}

/**
 * @description 劳务用工批量删除
 * @param {string} ids
 */
export const remove = (ids) => {
  return request({
    url: '/api/blade-labor/labor/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

/**
 * @description 劳务用工新增
 * @param {object} data
 */
export const add = (data) => {
  return request({
    url: '/api/blade-labor/labor/save',
    method: 'post',
    data: data
  })
}

/**
 * @description 劳务用工更新
 * @param {object} row
 */
export const update = (row) => {
  return request({
    url: '/api/blade-labor/labor/update',
    method: 'post',
    data: row
  })
}

/**
 * @description 劳务用工审核
 * @param {object} data
 */
export const infoCheck = (data) => {
  return request({
    url: '/api/blade-labor/labor/check',
    method: 'post',
    data
  })
}


/**
 * @desc 获取地区树
 * @param {number} data 该用户tenantid
 * @returns {Promise<ResponseData>}
 */
export const getTreeList = (data) => {
  return request({
    url: '/api/blade-system/dept/tree',
    method: 'get',
    params: {
      tenantId: data
    }
  })
}