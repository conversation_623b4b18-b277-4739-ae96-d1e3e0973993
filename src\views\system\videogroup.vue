<template>
  <basic-container>
    <avue-crud :option="option" :data="data" :table-loading="loading" :page.sync="page" :search.sync="search" ref="crud" v-model="form" @selection-change="selectionChange" @search-change="searchChange" @search-reset="searchReset" @refresh-change="refreshChange" @on-load="onLoad" :before-open="beforeOpen">
      <template slot="menuLeft">
        <el-button type="success" size="small" plain icon="el-icon-refresh" @click="handleSync">
          同步
        </el-button>
        <!-- <el-button type="warning" size="small" plain icon="el-icon-help" @click="handleBind">
          批量绑定
        </el-button> -->
      </template>

      <template slot-scope="{ row }" slot="state">
        <el-tag :type="row.state === 1 ? 'warning' : row.state === 2 ? 'success': 'danger'">
          {{ row.$state }}
        </el-tag>
      </template>

      <!-- <template slot="deptNamesForm">
        <div>
          <el-tag v-for="dept in form.deptNames" :key="dept.id">{{ dept }}</el-tag>
        </div>
      </template> -->

      <template slot="menu" slot-scope="{row}">
        <el-button type="text" size="mini" @click="handleSyncOne(row)" icon="el-icon-refresh">同 步</el-button>
        <el-button type="text" size="mini" @click="openBindDialog(row)" icon="el-icon-connection">绑 定</el-button>
      </template>
    </avue-crud>
    <el-dialog :visible.sync="dialogVisible" title="绑定部门" width="600px" append-to-body>
      <el-cascader v-model="bindDept" :options="regionOptions" :props="cascaderProps"  placeholder="请选择部门" ref="cascader" style="width: 100%" @change="handleRegionChange" clearable></el-cascader>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleBindCancel">取 消</el-button>
        <el-button type="primary" :loading="bindSaving" @click="handleBindSaveOne">确 定</el-button>
      </span>
    </el-dialog>
    <!-- <el-dialog :visible.sync="bindDialogVisible" title="设备绑定部门" width="600px" append-to-body>
      <div style="display:flex;align-items:stretch;">
        <div style="flex:1 1 0;min-width:220px;max-width:260px;border-right:1px solid #eee;padding-right:16px;display:flex;flex-direction:column;">
          <el-input v-model="filterText" placeholder="输入关键字筛选部门" size="small" clearable style="margin-bottom:8px;" />
          <div style="height:330px;overflow-y:auto;border:1px solid #e4e7ed;border-radius:4px;padding:8px;">
            <el-tree v-loading="deptLoading" :data="deptTree" node-key="id" show-checkbox check-strictly :default-checked-keys="checkedDeptIds" :props="{ label: 'title', children: 'children', value: 'id' }" ref="deptTree" @check="handleDeptCheck" :filter-node-method="filterNode" />
          </div>
        </div>
        <div style="flex:1 1 0;padding-left:16px;display:flex;flex-direction:column;">
          <div style="display:flex;justify-content:space-between;align-items:center;margin-bottom:12px;margin-top:5px;">
            <span style="font-weight:bold;">已选择部门<span v-if="!bindRow">(批量绑定为覆盖操作)</span></span>
            <el-button v-if="checkedDeptNames.length > 0" type="text" size="mini" @click="clearAllSelected" style="color:#f56c6c;padding:0;">
              一键清空
            </el-button>
          </div>
          <div style="height:335px;overflow-y:auto;border:1px solid #e4e7ed;border-radius:4px;padding:8px;">
            <el-tag v-for="(dept, idx) in checkedDeptList" :key="dept.id" type="info" closable :disable-transitions="true" @close="removeSelectedDept(idx)" style="margin:4px 8px 4px 0;cursor:pointer;">
              {{ dept.name }}
            </el-tag>
            <div v-if="!checkedDeptNames.length" style="color:#bbb;text-align:center;padding:20px 0;">暂无选择部门</div>
          </div>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="bindDialogVisible = false">取 消</el-button>
        <el-button type="primary" :loading="bindSaving" @click="handleBindSave">确 定</el-button>
      </span>
    </el-dialog> -->
  </basic-container>
</template>

<script>
import { getList, getVideoDetail, syncVideoList, bindVideo,syncVideoOne } from '@/api/system/video'
import { getDeptUserTree } from "@/api/system/dept";
export default {
  name: 'VideoGroup',
  data() {
    return {
      form: {},
      search: {},
      loading: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      // 假数据
      data: [],
      option: {
        height: 'auto',
        calcHeight: 30,
        tip: false,
        searchShow: true,
        searchMenuSpan: 4,
        border: true,
        index: true,
        selection: false,
        addBtn: false,
        viewBtn: true,
        editBtn: false,
        delBtn: false,
        dialogClickModal: false,
        labelWidth: 120,
        column: [
          // {
          //   label: '国标编码',
          //   prop: 'gbDeviceId',
          //   search: true,
          //   searchSpan: 6,
          //   maxlength: 32,
          // },
          {
            label: '设备名称',
            prop: 'deviceName',
            search: true,
            searchSpan: 6,
            maxlength: 50,
          },
          {
            label: '设备厂商',
            prop: 'manufacturer',
            hide:true,
          },
          {
            label: '设备类型',
            prop: 'deviceType',
            type: 'select',
            dicData: [
              { label: '国标摄像头', value: 1 },
              { label: 'RTMP 摄像头', value: 2 },
              { label: '国标平台', value: 3 },
              { label: 'NVR 设备', value: 4 },
            ],
          },
          {
            label: '设备描述',
            prop: 'deviceDescription',
            hide: true,
          },
          {
            label: '绑定部门',
            prop: 'deptName'
          },
          {
            label: '安装地址',
            prop: 'gbAddress'
          },
          {
            label: '设备状态',
            prop: 'state',
            type: 'select',
            searchSpan: 4,
            dicData: [
              { label: '未注册', value: 1 },
              { label: '在线', value: 2 },
              { label: '离线', value: 3 },
            ],
            search: true
          },
          // {
          //   label: '经度',
          //   prop: 'gbLongitude',
          //   type: 'number',
          //   hide: true,
          // },
          // {
          //   label: '纬度',
          //   prop: 'gbLatitude',
          //   type: 'number',
          //   hide: true,
          // },

          // {
          //   label: '创建时间',
          //   prop: 'createTime',
          //   type: 'datetime',
          //   format: 'yyyy-MM-dd HH:mm:ss',
          //   valueFormat: 'yyyy-MM-dd HH:mm:ss',
          // },
          // {
          //   label: '更新时间',
          //   prop: 'updateTime',
          //   type: 'datetime',
          //   format: 'yyyy-MM-dd HH:mm:ss',
          //   valueFormat: 'yyyy-MM-dd HH:mm:ss',
          // },

          {
            label: '上次同步时间',
            prop: 'updateTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
          },
          // {
          //   label: '是否绑定',
          //   prop: 'isBind',
          //   type: 'select',
          //   search: true,
          //   searchSpan: 4,
          //   dicData: [
          //     { label: '是', value: true },
          //     { label: '否', value: false }
          //   ]
          // },
          // {
          //   label: '绑定部门',
          //   prop: 'deptNames',
          //   formslot: true,
          //   span: 24,
          //   hide: true,
          //   type: 'textarea',
          //   dataType: 'array',
          // },
        ]
      },
      // 只保留单一弹窗和变量
      bindDialogVisible: false,
      bindRow: null,
      deptTree: [],
      deptLoading: false,
      checkedDeptIds: [],
      checkedDeptNames: [],
      checkedDeptList: [], // 存储完整的部门信息，包含id和name
      bindSaving: false,
      filterText: '', // 组织树筛选关键字
      selectionList:[],


      dialogVisible:false,
      bindDept:'',
      regionOptions: [],
      cascaderProps: {
        value: 'id',
        label: 'title',
        multiple:false,
        checkStrictly: true,
        emitPath: false,
      }

    }
  },
  // 生命周期钩子，页面加载时获取数据
  created() {
    this.init()
  },
  computed: {
    idsArray() {
      let ids = [];
      // console.log(this.selectionList)
      this.selectionList.forEach(ele => {
        ids.push(ele.gbId);
      });
      return ids;
    }
  },
  watch: {
    filterText(val) {
      this.$refs.deptTree && this.$refs.deptTree.filter(val);
    },
  },
  methods: {
    handleRegionChange(data){
      console.log('选中的行政区划:', data);
    },
    async init(){
      try {
        // 查详情，拿到已绑定部门
        // const detailRes = await getVideoDetail({ gbId: row.gbId })
        // if (row.createDept) {
        //   this.checkedDeptIds = [row.createDept] || []
        //   this.checkedDeptNames = [row.deptName] || []
        //   this.checkedDeptList = this.checkedDeptNames.map((name, index) => ({
        //     id: this.checkedDeptIds[index] || '',
        //     name: name
        //   }))
        // }
        // 查部门树
        const treeRes = await getDeptUserTree()
        if (treeRes && treeRes.data.success) {
          this.regionOptions = treeRes.data.data
        }
      } catch (e) {
        this.$message.error('获取部门数据失败')
      }
    },
    // 获取设备列表数据
    async onLoad(page, params = {}) {
      this.loading = true
      try {
        const res = await getList(page.currentPage, page.pageSize, Object.assign(params, this.query))
        // console.log(res)
        if (res && res.data.success) {
          this.data = res.data.data.records
          this.page.total = res.data.data.total || 0
        }
      } catch (e) {
        this.$message.error('获取设备列表失败')
        this.data = []
        this.page.total = 0
      } finally {
        this.loading = false
      }
    },
    // 搜索变化
    searchChange(params,done) {
      this.query = params
      this.page.currentPage = 1
      this.onLoad(this.page, params).then(() => done())
    },

    selectionChange(list) {
      // console.log(list)
      this.selectionList = list;
    },
    // 搜索重置
    searchReset() {
      this.search = {}
      this.query = {}
      this.page.currentPage = 1
      this.onLoad(this.page)
      // this.$message.success('搜索已重置')
    },

    // 刷新
    refreshChange() {
      this.onLoad(this.page, this.query);
      // this.onLoad()
      // this.$message.success('数据已刷新')
    },

    // 分页变化
    pageChange(val) {
      this.page.currentPage = val.currentPage
      this.page.pageSize = val.pageSize
      // this.onLoad()
    },

    async handleSync() {
      this.loading = true;
      // syncVideoList().then(res => {
      //   console.log(res.)
      // })
      try {
        const res = await syncVideoList();

        if (res && res.status === 200) {
          this.$message.success('同步任务已提交，请稍后刷新页面查看最新数据');
        } else {
          this.$message.error(res && res.data.msg ? res.data.msg : '同步失败');
        }
      } catch (e) {
        this.$message.error('同步失败');
      } finally {
        this.loading = false;
      }
    },

    async handleSyncOne(row) {
      this.loading = true;
      // syncVideoList().then(res => {
      //   console.log(res.)
      // })
      try {
        const res = await syncVideoOne(row.id);

        if (res && res.status === 200) {
          this.$message.success('同步任务已提交，请稍后刷新页面查看最新数据');
        } else {
          this.$message.error(res && res.data.msg ? res.data.msg : '同步失败');
        }
      } catch (e) {
        this.$message.error('同步失败');
      } finally {
        this.loading = false;
      }
    },

    beforeOpen(done, type) {
      if (["view"].includes(type)) {
        getVideoDetail(this.form.id).then(res => {
          if (res && res.data.success) {
            this.form = { ...res.data.data }
          } else {
            this.$message.error('获取详情失败')
          }
          done()
        }).catch(() => {
          this.$message.error('获取详情失败')
          done()
        })
      } else {
        done()
      }
    },

    async openBindDialog(row) {
      this.bindRow = row
      // this.deptTree = []
      // this.checkedDeptIds = []
      // this.checkedDeptNames = []
      // this.checkedDeptList = []
      // this.deptLoading = true
      try {
        // 查详情，拿到已绑定部门
        // const detailRes = await getVideoDetail({ gbId: row.gbId })
        // if (row.createDept) {
        //   this.checkedDeptIds = [row.createDept] || []
        //   this.checkedDeptNames = [row.deptName] || []
        //   this.checkedDeptList = this.checkedDeptNames.map((name, index) => ({
        //     id: this.checkedDeptIds[index] || '',
        //     name: name
        //   }))
        // }
        this.bindDept = row.createDept
        // 查部门树
        // const treeRes = await getDeptUserTree()
        // if (treeRes && treeRes.data.success) {
        //   this.regionOptions = treeRes.data.data
        // }
      } catch (e) {
        this.$message.error('获取部门数据失败')
      } finally {
        // this.deptLoading = false
        this.dialogVisible = true
      }
    },

    // async openBindDialog(row) {
    //   this.bindRow = row
    //   this.deptTree = []
    //   this.checkedDeptIds = []
    //   this.checkedDeptNames = []
    //   this.checkedDeptList = []
    //   this.deptLoading = true
    //   try {
    //     // 查详情，拿到已绑定部门
    //     // const detailRes = await getVideoDetail({ gbId: row.gbId })
    //     if (row.createDept) {
    //       this.checkedDeptIds = [row.createDept] || []
    //       this.checkedDeptNames = [row.deptName] || []
    //       this.checkedDeptList = this.checkedDeptNames.map((name, index) => ({
    //         id: this.checkedDeptIds[index] || '',
    //         name: name
    //       }))
    //     }
    //     // 查部门树
    //     const treeRes = await getDeptUserTree()
    //     if (treeRes && treeRes.data.success) {
    //       this.deptTree = treeRes.data.data
    //     }
    //   } catch (e) {
    //     this.$message.error('获取部门数据失败')
    //   } finally {
    //     this.deptLoading = false
    //     this.bindDialogVisible = true
    //   }
    // },
    handleDeptCheck() {
      const tree = this.$refs.deptTree;
      if (tree) {
        this.checkedDeptIds = tree.getCheckedKeys();
        const checkedNodes = tree.getCheckedNodes();
        this.checkedDeptNames = checkedNodes.map(n => n.title || n.label || n.name);
        this.checkedDeptList = checkedNodes.map(n => ({
          id: n.id,
          name: n.title || n.label || n.name
        }));
      }
    },
    clearAllSelected() {
      this.checkedDeptIds = [];
      this.checkedDeptNames = [];
      this.checkedDeptList = [];
      const tree = this.$refs.deptTree;
      if (tree) {
        tree.setCheckedKeys([]);
      }
    },
    removeSelectedDept(index) {
      this.checkedDeptList.splice(index, 1);
      this.checkedDeptIds = this.checkedDeptList.map(dept => dept.id);
      this.checkedDeptNames = this.checkedDeptList.map(dept => dept.name);
      const tree = this.$refs.deptTree;
      if (tree) {
        tree.setCheckedKeys(this.checkedDeptIds);
      }
    },
    handleBindCancel() {
      this.dialogVisible = false;
      this.bindDept = ""

    },
    async handleBindSaveOne(){
       this.bindSaving = true
       console.log(this.bindRow,this.bindDept)
       if(!this.bindDept){
        this.$message.info("请选择部门")
        this.bindSaving = false
        return
       }
      try {
        const res = await bindVideo(this.bindRow.id,this.bindDept)
        if (res && res.data.success) {
          this.$message.success('绑定成功')
          this.dialogVisible = false
          this.onLoad(this.page,this.query)
        } else {
          this.$message.error(res && res.msg ? res.msg : '绑定失败')
        }
      } catch (e) {
        this.$message.error('绑定失败')
      } finally {
        this.bindSaving = false
      }
    },
    async handleBindSave() {
      // 批量绑定
      if (!this.bindRow) {

        this.bindSaving = true;
        try {
        const res = await bindVideo({ gbIds: this.idsArray, deptIds: this.checkedDeptIds })
          if (res && res.data.success) {
            this.$message.success('绑定成功')
            this.bindDialogVisible = false
            this.onLoad(this.page,this.query)
          } else {
            this.$message.error(res && res.msg ? res.msg : '绑定失败')
          }
        } catch (e) {
          this.$message.error('绑定失败')
        } finally {
          this.bindSaving = false
        }
        return;
      }
      // 单条绑定逻辑
      this.bindSaving = true
      try {
        const res = await bindVideo({ gbIds: [this.bindRow.gbId], deptIds: this.checkedDeptIds })
        if (res && res.data.success) {
          this.$message.success('绑定成功')
          this.bindDialogVisible = false
          this.onLoad(this.page,this.query)
        } else {
          this.$message.error(res && res.msg ? res.msg : '绑定失败')
        }
      } catch (e) {
        this.$message.error('绑定失败')
      } finally {
        this.bindSaving = false
      }
    },
    filterNode(value, data) {
      if (!value) return true;
      return (data.title || data.label || data.name || '').indexOf(value) > -1;
    },
    handleBind() {
      // 获取多选数据
      if (this.selectionList.length === 0) {
        this.$message.warning('请至少选择一条设备');
        return;
      }
      this.bindRow = null;
      this.checkedDeptIds = [];
      this.checkedDeptNames = [];
      this.checkedDeptList = [];
      this.filterText = '';
      this.deptLoading = true;
      // 只查一次部门树，不查详情
      getDeptUserTree().then(res => {
        if (res && res.data.success) {
          this.deptTree = res.data.data;
        }
      }).finally(() => {
        this.deptLoading = false;
        this.bindDialogVisible = true;
      });
    },
  }
}
</script>

<style scoped>
.el-tag {
  margin-right: 5px;
}
/* 美化滚动条 */
.el-tree {
  scrollbar-width: thin;
  scrollbar-color: #bbb #f5f5f5;
}
.el-tree::-webkit-scrollbar {
  width: 6px;
  background: #f5f5f5;
}
.el-tree::-webkit-scrollbar-thumb {
  background: #bbb;
  border-radius: 3px;
}

/* 弹窗中树容器的滚动条样式 */
.el-dialog .el-tree {
  scrollbar-width: thin;
  scrollbar-color: #bbb #f5f5f5;
}
.el-dialog .el-tree::-webkit-scrollbar {
  width: 6px;
  background: #f5f5f5;
}
.el-dialog .el-tree::-webkit-scrollbar-thumb {
  background: #bbb;
  border-radius: 3px;
}

/* 确保树容器有明确的滚动区域 */
.el-dialog div[style*="overflow-y:auto"] {
  scrollbar-width: thin;
  scrollbar-color: #bbb #f5f5f5;
}
.el-dialog div[style*="overflow-y:auto"]::-webkit-scrollbar {
  width: 6px;
  background: #f5f5f5;
}
.el-dialog div[style*="overflow-y:auto"]::-webkit-scrollbar-thumb {
  background: #bbb;
  border-radius: 3px;
}

.selected-dept-scroll {
  scrollbar-width: thin;
  scrollbar-color: #bbb #f5f5f5;
}
.selected-dept-scroll::-webkit-scrollbar {
  width: 8px;
  background: #f5f5f5;
}
.selected-dept-scroll::-webkit-scrollbar-thumb {
  background: #bbb;
  border-radius: 4px;
}
</style>
