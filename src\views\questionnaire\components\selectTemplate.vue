<template>
 <el-dialog
    title="选择选项"
    :visible.sync="showSelectDialog"
    width="50%"
    :modal="false"
    :modal-append-to-body="false"
    :close-on-click-modal="false"
    @close="handleClose"
  >
  <div class="select-container">
    <el-select
      v-model="selectedValue"
      placeholder="请选择或搜索"
      ref="select"
      filterable
      remote
      clearable
      v-loadmore="loadData"
      :remote-method="remoteSearch"
      :loading="searchLoading"
      style="width: 100%;"
    >
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      ></el-option>
      <div v-if="loading && !searching" class="loading-indicator">加载中...</div>
      <div v-if="!hasMore && options.length > 0 && !searching" class="no-more">没有更多数据</div>
      <div v-if="searching && !loading && options.length === 0" class="no-result">没有匹配结果</div>
    </el-select>
  </div>
  <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="confirmSelection">确认</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { getTemplateList} from '@/api/questionnaire/survey'
export default {
  name: 'ScrollLoadSelect',
  props: {
    // 搜索防抖时间(ms)
    debounceTime: {
      type: Number,
      default: 300
    },
    showSelectDialog: {
      type: Boolean,
      default: false
    }
  },
 directives: {
    loadmore: {
      bind: (el, binding) => {
        const SELECTWRAP = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap')
        SELECTWRAP.addEventListener('scroll', function() {
          if (this.scrollHeight - this.scrollTop <= this.clientHeight) {
            
            binding.value()
          }
        })
      }
    }
  },
  data() {
    return {
      selectedValue: null,
      options: [],
      currentPage: 1,
      pageSize: 10,
      total: 0,
      loading: false,
      hasMore: true,
      dropdownPanel: null,
      searchKeyword: '',
      searchLoading: false,
      searching: false,
      debounceTimer: null,
    };
  },
  beforeDestroy() {
    if (this.debounceTimer) clearTimeout(this.debounceTimer);
  },
  mounted(){
    this.remoteSearch()
  },
  watch:{
    showSelectDialog:{
      handler(nV,oV){
        if(nV){
            this.selectedValue=''
            this.remoteSearch('')
        }
      },immediate:true
    }
  },
  methods: {
    handleClose(){
      this.selectedValue=''
      this.$emit('update:showSelectDialog', false)
    },
    confirmSelection() {
      console.log(this.selectedValue,'selectValue')
      // this.$emit('update:showSelectDialog', false)
      this.$emit('confirmSelect', this.selectedValue);
    },
    handleOptionSelect(value) {
      // 选择项变化时的处理
      this.selectedValue = value;
      this.onSelect(value);
    },
    // 远程搜索方法(带防抖)
    remoteSearch(keyword = '') {
      this.searchKeyword = keyword.trim();
      this.hasMore=true
      // 清除之前的定时器
      if (this.debounceTimer) clearTimeout(this.debounceTimer);

      // 空关键词时恢复默认列表
      if (!this.searchKeyword) {
        this.searching = false;
        this.options = [];
        this.currentPage = 1;
        this.loadData();
        console.log('inti')
        return;
      }

      // 防抖处理
      this.debounceTimer = setTimeout(() => {
        this.searching = true;
        this.currentPage = 1;
        this.options = [];
        this.loadData();
      }, this.debounceTime);
    },
    async loadData() {
      if(!this.hasMore){
        return
      }
      try {
        const params = {};
        if (this.searching && this.searchKeyword) {
          params.title = this.searchKeyword;
        }
        const res = await getTemplateList(this.currentPage,this.pageSize,params);
        const data =res.data.data
        this.total = data.total;
        const records = data.records;
        var newOptions=records.map(item=>{
          return {
            label:item.title,
            value:item.id
          }
        })
        if (this.currentPage === 1) {
          this.options = newOptions;
        } else {
          this.options = [...this.options, ...newOptions];
        }

        this.hasMore = this.options.length < this.total;
        this.currentPage++;
      } catch (error) {
        console.error('加载数据失败:', error);
      } finally {
        // this.loading = false;
        // this.searchLoading = false;
      }
    },
  }
};
</script>

<style scoped>
::v-deep .el-select-dropdown__wrap {
  max-height: 300px;
}

.loading-indicator,
.no-more,
.no-result {
  text-align: center;
  padding: 8px 0;
  color: #606266;
  font-size: 12px;
}

.no-result {
  color: #f56c6c;
}

.select-container {
  margin-bottom: 20px;
}
</style>